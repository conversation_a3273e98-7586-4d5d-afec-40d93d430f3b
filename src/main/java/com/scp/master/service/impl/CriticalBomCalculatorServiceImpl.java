package com.scp.master.service.impl;

import com.adm.system.service.ISystemService;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.scp.master.dao.ICriticalBomCalculatorDao;
import com.scp.master.service.ICriticalBomCalculatorService;
import com.scp.simulation.bean.*;
import com.scp.simulation.feign.BCDCFeignClient;
import com.starter.context.bean.Configuration;
import com.starter.context.bean.Message;
import com.starter.context.bean.Response;
import com.starter.context.bean.SimplePage;
import com.starter.context.bean.scptable.ScpTableHelper;
import com.starter.context.configuration.MqttConfiguration;
import com.starter.context.configuration.database.DatabaseType;
import com.starter.context.configuration.database.TargetDataSource;
import com.starter.context.configuration.database.DatabaseContextHolder;
import com.starter.context.utils.SpringContextUtils;
import com.starter.context.mail.MailBean;
import com.starter.context.mail.MailFeignClient;
import com.starter.utils.Utils;
import com.starter.utils.excel.ExcelTemplate;
import com.starter.utils.excel.SimpleSheetContentsHandler;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.xssf.model.StylesTable;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Service("criticalBomCalculatorService")
@Scope("prototype")
@Transactional
public class CriticalBomCalculatorServiceImpl implements ICriticalBomCalculatorService {

    @Resource
    private Response response;

    @Resource
    private ICriticalBomCalculatorDao criticalBomCalculatorDao;

    @Resource
    private ExcelTemplate excelTemplate;

    @Resource
    private BCDCFeignClient bcdcFeignClient;

    @Resource
    private ScpTableHelper scpTableHelper;

    @Resource
    private ISystemService systemService;

    @Resource
    private MailFeignClient mailFeignClient;

    private final static SimpleDateFormat DEFAULT_DATE_FORMAT = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss");

    public final static String PARENT_CODE = "menu270";

    @Override
    public Response initPage(String userid) {
        Map<String, Object> resultMap = new HashMap<>();
        List<Map<String, String>> filterList = criticalBomCalculatorDao.queryFilterList();
        List<Map<String, String>> moList = new ArrayList<>();
        List<Map<String, String>> soList = new ArrayList<>();
        List<Map<String, String>> poList = new ArrayList<>();

        for (Map<String, String> map : filterList) {
            if ("MO".equals(map.get("TYPE"))) {
                moList.add(map);
            } else if ("SO".equals(map.get("TYPE"))) {
                soList.add(map);
            } else if ("PO".equals(map.get("TYPE"))) {
                poList.add(map);
            }
        }

        resultMap.put("moList", Utils.parseCascader(moList, false));
        resultMap.put("soList", Utils.parseCascader(soList, false));
        resultMap.put("poList", Utils.parseCascader(poList, false));
        resultMap.put("plantList", criticalBomCalculatorDao.queryAvailablePlants());
        return response.setBody(resultMap);
    }

    @Override
    public Response queryBatchList(String userid) {
        String authType = StringUtils.upperCase(StringUtils.trim(criticalBomCalculatorDao.queryAuthDetails(userid, PARENT_CODE)));
        List<String> batch_id=criticalBomCalculatorDao.queryShareAuth(userid);
        return response.setBody(Utils.parseTreeNodes(criticalBomCalculatorDao.queryUserBatchList(userid, authType,batch_id)));
    }

    @Override
    public Response queryBatchInfo(Map<String, Object> parameterMap) {
        Map<String, Object> info = criticalBomCalculatorDao.queryBatchInfo(parameterMap);
        JSONObject params = JSONObject.parseObject((String) info.get("PARAMS"));
        Map<String, Object> runInfo = new HashMap<>();
        runInfo.put("step", info.get("STEP"));
        runInfo.put("stepMode", info.get("STEP_MODE"));
        params.put("_runInfo", runInfo);

        info.put("PARAMS", JSONObject.toJSONString(params, true));
        return response.setBody(info);
    }

    @Override
    public Response initExistsGroup(String userid) {
        String authType = StringUtils.upperCase(StringUtils.trim(criticalBomCalculatorDao.queryAuthDetails(userid, PARENT_CODE)));
        return response.setBody(criticalBomCalculatorDao.initExistsGroup(userid, authType));
    }

    @Override
    public Response queryTaskInfo(String userid) {
        Map<String, Object> resultMap = new HashMap<>();
        String authType = StringUtils.upperCase(StringUtils.trim(criticalBomCalculatorDao.queryAuthDetails(userid, PARENT_CODE)));
        List<String> batch_id=criticalBomCalculatorDao.queryShareAuth(userid);
        List<Map<String, String>> result = criticalBomCalculatorDao.queryTaskInfo(userid, authType,batch_id);
        List<Map<String, String>> nameList = new ArrayList<>();
        Map<String, Object> taskInfo = new HashMap<>();

        for (Map<String, String> map : result) {
            nameList.add(map);
            taskInfo.put(map.get("BATCH_ID"), map.get("PARAMS"));
        }
        resultMap.put("nameList", Utils.parseCascader(nameList, false));
        resultMap.put("taskInfo", taskInfo);

        return response.setBody(resultMap);
    }

    @Override
    public Response deleteBatch(Map<String, Object> parameterMap, String userid) {
        parameterMap.put("userid", userid);
        if (criticalBomCalculatorDao.queryBatchCount(parameterMap) > 0) {
            criticalBomCalculatorDao.deleteBatch(parameterMap);
            response.setBody(0);
        } else {
            response.setBody(-1);
        }

        return response;
    }

    @Override
    public Response sendSimulateRequest(Map<String, Object> parameterMap, String userid) {
        parameterMap.put("userid", userid);
        // 检查目前有多少任务正在执行, 暂定8个
        int executeCnt = criticalBomCalculatorDao.queryExecutingCount(parameterMap);
        if (executeCnt > 8) {
            response.setBody("No more tasks can be submitted at this time(" + executeCnt + " tasks simulating)");
        }
        criticalBomCalculatorDao.updateSimulateStep(parameterMap);
        String batchId = (String) parameterMap.get("batchId");
        Integer balanceValue = (Integer) parameterMap.get("balanceValue");
        String params = criticalBomCalculatorDao.queryBatchParams(batchId);
        JSONObject jsonObject = JSONObject.parseObject(params);
        String name = jsonObject.getString("name");
        String type = jsonObject.getString("type");
        String strategy = jsonObject.getString("strategy");
        String plant = jsonObject.getString("plant");
        String module = jsonObject.getString("module");

        criticalBomCalculatorDao.updatePlant(plant,batchId);
        JSONArray stockConfig = jsonObject.getJSONArray("stockConfig");
        // 分批出货的时候, 回传给求解器的DEMAND_BASE应该是MAT
        Map<String, Object> logMap = new HashMap<>();
        logMap.put("batch_id", batchId);
        this.sendLog(batchId, userid, "sending request to ML module, " + JSON.toJSONString(logMap));
        try {
            BestCanDoCalculatorParam param = new BestCanDoCalculatorParam();
            param.setTask_name(name);
            param.setUser_id(userid);
            param.setBatch_id(batchId);
            param.setType(type);
            param.setStrategy(strategy);
            param.setPlant(plant);
            param.setModule(module);
            param.setBalanceValue(balanceValue);
            Response rep = bcdcFeignClient.execute(param);
            this.sendLog(batchId, userid, "request sent, " + JSON.toJSONString(rep));
        } catch (Exception e) {
            this.sendLog(batchId, userid, "request sent failed, " + e.getMessage());
        }
        response.setBody("0");

        return response;
    }

    @Override
    public Response saveNewBatch(Map<String, Object> parameterMap, String userid) {
        this.generateNewBatchCascaderFilter(parameterMap, "priority");
        this.generateNewBatchCascaderFilter(parameterMap, "po");

        // create batch id
        String batchID = Utils.randomStr(12);
        String name = (String) parameterMap.get("name");
        Map<String, Object> params = new HashMap<>();
        params.put("name", parameterMap.get("name"));
        params.put("type", parameterMap.get("type"));
        params.put("plant",parameterMap.get("plant"));
        params.put("strategy",parameterMap.get("strategy"));
        params.put("database",parameterMap.get("database"));
        params.put("po", parameterMap.get("po"));
        params.put("stockConfig", parameterMap.get("stockConfig"));
        params.put("dateRange", parameterMap.get("dateRange"));

        parameterMap.put("params", JSON.toJSONString(params));
        parameterMap.put("module", parameterMap.get("module"));
        parameterMap.put("batch_id", batchID);
        parameterMap.put("userid", userid);

        criticalBomCalculatorDao.insertLog(parameterMap);
        this.sendLog(batchID, userid, "new task lauched, task mode: " + name + ", batch id: " + batchID);

        int cnt = criticalBomCalculatorDao.copyPOData(parameterMap);
        this.sendLog(batchID, userid, (Utils.thousandBitSeparator(String.valueOf(cnt)) + (cnt > 1 ? " lines" : " line")) + " po copied from po_management_abla_overdue_v to bcd_input_po");

        // 通过Spring代理对象调用方法，确保AOP切面生效
        try {
            ICriticalBomCalculatorService proxyService = SpringContextUtils.getBean(ICriticalBomCalculatorService.class);
            this.sendLog(batchID, userid, "通过Spring代理对象调用insertBomReport2WithClickHouse方法");
            proxyService.insertBomReport2WithClickHouse(parameterMap);
        } catch (Exception e) {
            this.sendLog(batchID, userid, "调用insertBomReport2WithClickHouse失败: " + e.getMessage());
            throw e;
        }

        return response.setBody(batchID);
    }

    @Override
    @TargetDataSource(DatabaseType.CLICKHOUSE)
    public void insertBomReport2WithClickHouse(Map<String, Object> parameterMap) {
        String batchId = (String) parameterMap.get("batch_id");
        String userid = (String) parameterMap.get("userid");

        // 记录当前数据源（应该已经被AOP切换到ClickHouse）
        DatabaseType currentDataSource = DatabaseContextHolder.getDatabaseType();
        this.sendLog(batchId, userid, "AOP切面已切换数据源 - 当前数据库类型: " + currentDataSource);

        try {
            this.sendLog(batchId, userid, "开始执行insertBomReport2，使用ClickHouse数据库");
            criticalBomCalculatorDao.insertBomReport2(parameterMap);
            this.sendLog(batchId, userid, "insertBomReport2执行成功");
        } catch (Exception e) {
            this.sendLog(batchId, userid, "insertBomReport2执行失败: " + e.getMessage());
            throw e;
        }
    }

    @Override
    @TargetDataSource(DatabaseType.CLICKHOUSE)
    public Response queryStart(Map<String, Object> parameterMap) {
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setTotal(criticalBomCalculatorDao.queryStartCount(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(criticalBomCalculatorDao.queryStart(parameterMap));
        }
        return response.setBody(page);
    }

    @Override
    @TargetDataSource(DatabaseType.CLICKHOUSE)
    public void downloadStart(Map<String, Object> parameterMap, HttpServletResponse res) {
        SimplePage<Map<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        String fileName = "priority_" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(res, fileName, "com.scp.simulation.dao.IcriticalBomCalculatorDao.downloadStart", parameterMap);
    }

    @Override
    @TargetDataSource(DatabaseType.CLICKHOUSE)
    public Response saveStart(Map<String, Object> parameterMap) {
        String batchId = (String) parameterMap.get("batchId");
        scpTableHelper.setScpTableInsertHandler((headers, creates) -> {
            return criticalBomCalculatorDao.createStartByTable(headers, creates, batchId);
        });
        scpTableHelper.setScpTableDeleteHandler(deletes -> criticalBomCalculatorDao.deleteStartByTable(deletes, batchId));
        scpTableHelper.setScpTableUpdateHandler((pk, updates) -> criticalBomCalculatorDao.updateStartByTable(pk, updates, batchId));
        Message message = scpTableHelper.execCRUD(parameterMap);
        return response.setBody(message);
    }

    @Override
    public Response queryReport1(Map<String, Object> parameterMap) {
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setTotal(criticalBomCalculatorDao.queryReport1Count(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(criticalBomCalculatorDao.queryReport1(parameterMap));
        }
        return response.setBody(page);
    }

    @Override
    public void downloadReport1(Map<String, Object> parameterMap, HttpServletResponse res) {
        SimplePage<Map<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        String fileName = "priority_" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(res, fileName, "com.scp.simulation.dao.IcriticalBomCalculatorDao.downloadReport1", parameterMap);
    }

    @Override
    public Response saveReport1(Map<String, Object> parameterMap) {
        String batchId = (String) parameterMap.get("batchId");
        scpTableHelper.setScpTableInsertHandler((headers, creates) -> {
            if (headers.contains("PRIMARY_KEY") == false) {
                headers.add("PRIMARY_KEY");

            }
            for (Map<String, Object> map : creates) {
                if (StringUtils.isBlank((String) map.get("PRIMARY_KEY"))) {
                    map.put("PRIMARY_KEY", Utils.randomStr(16));
                }
            }

            return criticalBomCalculatorDao.createReport1ByTable(headers, creates, batchId);
        });
        scpTableHelper.setScpTableDeleteHandler(deletes -> criticalBomCalculatorDao.deleteReport1ByTable(deletes, batchId));
        scpTableHelper.setScpTableUpdateHandler((pk, updates) -> criticalBomCalculatorDao.updateReport1ByTable(pk, updates, batchId));
        Message message = scpTableHelper.execCRUD(parameterMap);
        return response.setBody(message);
    }

    @Override
    public Response queryReport2(Map<String, Object> parameterMap) {
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setTotal(criticalBomCalculatorDao.queryReport2Count(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(criticalBomCalculatorDao.queryReport2(parameterMap));
        }
        return response.setBody(page);
    }

    @Override
    public void downloadReport2(Map<String, Object> parameterMap, HttpServletResponse res) {
        SimplePage<Map<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        String fileName = "bom_list_" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(res, fileName, "com.scp.simulation.dao.IcriticalBomCalculatorDao.downloadReport2", parameterMap);
    }

    @Override
    public Response saveReport2(Map<String, Object> parameterMap) {
        String batchId = (String) parameterMap.get("batchId");
        scpTableHelper.setScpTableInsertHandler((headers, creates) -> criticalBomCalculatorDao.createReport2ByTable(headers, creates, batchId));
        scpTableHelper.setScpTableDeleteHandler(deletes -> criticalBomCalculatorDao.deleteReport2ByTable(deletes, batchId));
        scpTableHelper.setScpTableUpdateHandler((pk, updates) -> criticalBomCalculatorDao.updateReport2ByTable(pk, updates, batchId));
        Message message = scpTableHelper.execCRUD(parameterMap);
        return response.setBody(message);
    }

    @Override
    public Response queryReport11(Map<String, Object> parameterMap) {
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setTotal(criticalBomCalculatorDao.queryReport11Count(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(criticalBomCalculatorDao.queryReport11(parameterMap));
        }
        return response.setBody(page);
    }

    @Override
    public void downloadReport11(Map<String, Object> parameterMap, HttpServletResponse res) {
        SimplePage<Map<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        String fileName = "bom_list_" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(res, fileName, "com.scp.simulation.dao.IcriticalBomCalculatorDao.downloadReport11", parameterMap);
    }

    @Override
    public Response saveReport11(Map<String, Object> parameterMap) {
        String batchId = (String) parameterMap.get("batchId");
        scpTableHelper.setScpTableInsertHandler((headers, creates) -> criticalBomCalculatorDao.createReport11ByTable(headers, creates, batchId));
        scpTableHelper.setScpTableDeleteHandler(deletes -> criticalBomCalculatorDao.deleteReport11ByTable(deletes, batchId));
        scpTableHelper.setScpTableUpdateHandler((pk, updates) -> criticalBomCalculatorDao.updateReport11ByTable(pk, updates, batchId));
        Message message = scpTableHelper.execCRUD(parameterMap);
        return response.setBody(message);
    }

    @Override
    public Response queryReport3(Map<String, Object> parameterMap) {
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setTotal(criticalBomCalculatorDao.queryReport3Count(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(criticalBomCalculatorDao.queryReport3(parameterMap));
        }
        return response.setBody(page);
    }

    @Override
    public void downloadReport3(Map<String, Object> parameterMap, HttpServletResponse res) {
        SimplePage<Map<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        String fileName = "recourse_input_" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(res, fileName, "com.scp.simulation.dao.IcriticalBomCalculatorDao.downloadReport3", parameterMap);
    }

    @Override
    public Response saveReport3(Map<String, Object> parameterMap) {
        String batchId = (String) parameterMap.get("batchId");
        scpTableHelper.setScpTableInsertHandler((headers, creates) -> criticalBomCalculatorDao.createReport3ByTable(headers, creates, batchId));
        scpTableHelper.setScpTableDeleteHandler(deletes -> criticalBomCalculatorDao.deleteReport3ByTable(deletes, batchId));
        scpTableHelper.setScpTableUpdateHandler((pk, updates) -> criticalBomCalculatorDao.updateReport3ByTable(pk, updates, batchId));
        Message message = scpTableHelper.execCRUD(parameterMap);
        return response.setBody(message);
    }

    @Override
    public Response queryReport9(Map<String, Object> parameterMap) {
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setTotal(criticalBomCalculatorDao.queryReport9Count(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(criticalBomCalculatorDao.queryReport9(parameterMap));
        }
        return response.setBody(page);
    }

    @Override
    public void downloadReport9(Map<String, Object> parameterMap, HttpServletResponse res) {
        SimplePage<Map<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        String fileName = "recourse_input_" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(res, fileName, "com.scp.simulation.dao.IcriticalBomCalculatorDao.downloadReport9", parameterMap);
    }

    @Override
    public Response saveReport9(Map<String, Object> parameterMap) {
        String batchId = (String) parameterMap.get("batchId");
        scpTableHelper.setScpTableInsertHandler((headers, creates) -> criticalBomCalculatorDao.createReport9ByTable(headers, creates, batchId));
        scpTableHelper.setScpTableDeleteHandler(deletes -> criticalBomCalculatorDao.deleteReport9ByTable(deletes, batchId));
        scpTableHelper.setScpTableUpdateHandler((pk, updates) -> criticalBomCalculatorDao.updateReport9ByTable(pk, updates, batchId));
        Message message = scpTableHelper.execCRUD(parameterMap);
        return response.setBody(message);
    }

    @Override
    @TargetDataSource(DatabaseType.CLICKHOUSE)
    public void downloadStartTemplate(Map<String, Object> parameterMap, HttpServletResponse response) {
        String fileName = "demand_start_list.xlsx";
        List<LinkedHashMap<String, Object>> resultList = new ArrayList<>();
        LinkedHashMap<String, Object> map = new LinkedHashMap<>();
        resultList.add(map);
        map.put("MATERIAL", null);
        map.put("PLANT_CODE", null);

        excelTemplate.create(response, fileName, resultList);
    }

    @Override
    public void downloadReport1Template(Map<String, Object> parameterMap, HttpServletResponse response) {
        String fileName = "demand_input.xlsx";
        List<LinkedHashMap<String, Object>> resultList = new ArrayList<>();
        LinkedHashMap<String, Object> map = new LinkedHashMap<>();
        resultList.add(map);
        map.put("PRIMARY_KEY", null);
        map.put("MATERIAL", null);
        map.put("PRIORITY", null);
        map.put("QTY", null);

        excelTemplate.create(response, fileName, resultList);
    }

    @Override
    public void downloadReport2Template(Map<String, Object> parameterMap, HttpServletResponse response) {
        String fileName = "bom_list_template.xlsx";
        List<LinkedHashMap<String, Object>> resultList = new ArrayList<>();
        LinkedHashMap<String, Object> map = new LinkedHashMap<>();
        resultList.add(map);
        map.put("MATERIAL", null);
        map.put("BOM_COMPONENT", null);
        map.put("USAGE", null);
        excelTemplate.create(response, fileName, resultList);
    }

    @Override
    public void downloadReport11Template(Map<String, Object> parameterMap, HttpServletResponse response) {
        String fileName = "Fix_recourse_template.xlsx";
        List<LinkedHashMap<String, Object>> resultList = new ArrayList<>();
        LinkedHashMap<String, Object> map = new LinkedHashMap<>();
        resultList.add(map);
        map.put("FIX_RECOURSE", null);
        excelTemplate.create(response, fileName, resultList);
    }

    @Override
    public void downloadReport3Template(Map<String, Object> parameterMap, HttpServletResponse response) {
        String fileName = "recourse_input_template.xlsx";
        List<LinkedHashMap<String, Object>> resultList = new ArrayList<>();
        LinkedHashMap<String, Object> map = new LinkedHashMap<>();
        resultList.add(map);
        map.put("MATERIAL", null);
        for(int j=0;j<=35;j++) {
            String a="STEP"+j;
            map.put(a, null);
        }
        excelTemplate.create(response, fileName, resultList);
    }

    @Override
    public void downloadReport9Template(Map<String, Object> parameterMap, HttpServletResponse response) {
        String fileName = "recourse_input_template.xlsx";
        List<LinkedHashMap<String, Object>> resultList = new ArrayList<>();
        LinkedHashMap<String, Object> map = new LinkedHashMap<>();
        resultList.add(map);
        map.put("BOM_COMPONENT", null);
        map.put("STEP0",null);
        excelTemplate.create(response, fileName, resultList);
    }

    @Override
    public void downloadReport7Template(Map<String, Object> parameterMap, HttpServletResponse response) {
        String fileName = "bcdc_input_substitution.xlsx";
        List<LinkedHashMap<String, Object>> resultList = new ArrayList<>();
        LinkedHashMap<String, Object> map = new LinkedHashMap<>();
        resultList.add(map);
        map.put("MATERIAL_IN_BOM", null);
        map.put("FOLLOW_UP_MATERIAL", null);
        excelTemplate.create(response, fileName, resultList);
    }

    @Override
    @TargetDataSource(DatabaseType.CLICKHOUSE)
    public Response uploadStart(String batchId, MultipartFile file) throws Exception {
        File tempFile = File.createTempFile(String.valueOf(System.currentTimeMillis()), ".tmp");
        file.transferTo(tempFile);

        List<DemandInput> data = new ArrayList<>();
        criticalBomCalculatorDao.deleteStartData(batchId);
        excelTemplate.read(tempFile, 1, new SimpleSheetContentsHandler() {
            @Override
            public void handleRow(int rowNum) {

                if (rowNum == 0) {
                    return;
                }

                int i = 0;
                DemandInput bean = new DemandInput();
                bean.setMaterial(row.get(i++));
                bean.setPlant_code(row.get(i++));
                data.add(bean);

                if (data.size() >= 1000) {
                    criticalBomCalculatorDao.insertStartData(batchId, data);
                    data.clear();
                }
            }
        }, new StylesTable());

        criticalBomCalculatorDao.insertStartData(batchId, data);
        return response;
    }

    @Override
    public Response uploadReport1(String batchId, MultipartFile file) throws Exception {
        File tempFile = File.createTempFile(String.valueOf(System.currentTimeMillis()), ".tmp");
        file.transferTo(tempFile);

        List<DemandInput> data = new ArrayList<>();
        criticalBomCalculatorDao.deleteReport1Data(batchId);
        excelTemplate.read(tempFile, 1, new SimpleSheetContentsHandler() {
            @Override
            public void handleRow(int rowNum) {

                if (rowNum == 0) {
                    return;
                }

                int i = 0;
                DemandInput bean = new DemandInput();
                bean.setPrimaryKey(row.get(i++));
                bean.setMaterial(row.get(i++));
                bean.setPriority(Utils.parseInt(row.get(i++)));
                bean.setQty(Utils.parseDouble(row.get(i++)));
                data.add(bean);

                if (data.size() >= 1000) {
                    criticalBomCalculatorDao.insertReport1Data(batchId, data);
                    data.clear();
                }
            }
        }, new StylesTable());

        criticalBomCalculatorDao.insertReport1Data(batchId, data);
        return response;
    }

    @Override
    public Response uploadReport2(String batchId, MultipartFile file) throws Exception {
        File tempFile = File.createTempFile(String.valueOf(System.currentTimeMillis()), ".tmp");
        file.transferTo(tempFile);

        List<BomInput> data = new ArrayList<>();
        criticalBomCalculatorDao.deleteReport2Data(batchId);
        excelTemplate.read(tempFile, 1, new SimpleSheetContentsHandler() {
            @Override
            public void handleRow(int rowNum) {

                if (rowNum == 0) {
                    return;
                }

                int i = 0;
                BomInput bean = new BomInput();
                bean.setMaterial(row.get(i++));
                bean.setBomComponent(row.get(i++));
                bean.setUsage(Double.parseDouble(row.get(i++)));
                data.add(bean);

                if (data.size() >= 1000) {
                    criticalBomCalculatorDao.insertReport2Data(batchId, data);
                    data.clear();
                }
            }
        }, new StylesTable());

        criticalBomCalculatorDao.insertReport2Data(batchId, data);
        return response;
    }

    @Override
    public Response uploadReport11(String batchId, MultipartFile file) throws Exception {
        File tempFile = File.createTempFile(String.valueOf(System.currentTimeMillis()), ".tmp");
        file.transferTo(tempFile);

        List<BomInput> data = new ArrayList<>();
        criticalBomCalculatorDao.deleteReport11Data(batchId);
        excelTemplate.read(tempFile, 1, new SimpleSheetContentsHandler() {
            @Override
            public void handleRow(int rowNum) {

                if (rowNum == 0) {
                    return;
                }

                int i = 0;
                BomInput bean = new BomInput();
                bean.setFixRecourse(row.get(i++));
                data.add(bean);

                if (data.size() >= 1000) {
                    criticalBomCalculatorDao.insertReport11Data(batchId, data);
                    data.clear();
                }
            }
        }, new StylesTable());

        criticalBomCalculatorDao.insertReport11Data(batchId, data);
        return response;
    }

    @Override
    public Response uploadReport3(String batchId, MultipartFile file) throws Exception {
        File tempFile = File.createTempFile(String.valueOf(System.currentTimeMillis()), ".tmp");
        file.transferTo(tempFile);

        List<RecourseInput> data = new ArrayList<>();
        criticalBomCalculatorDao.deleteReport3Data(batchId);
        excelTemplate.read(tempFile, 1, new SimpleSheetContentsHandler() {
            @Override
            public void handleRow(int rowNum) {

                if (rowNum == 0) {
                    return;
                }

                int i = 0;
                RecourseInput bean = new RecourseInput();
                bean.setMaterial(row.get(i++));
                bean.setStep0(row.get(i++));
                bean.setStep1(row.get(i++));
                bean.setStep2(row.get(i++));
                bean.setStep3(row.get(i++));
                bean.setStep4(row.get(i++));
                bean.setStep5(row.get(i++));
                bean.setStep6(row.get(i++));
                bean.setStep7(row.get(i++));
                bean.setStep8(row.get(i++));
                bean.setStep9(row.get(i++));
                bean.setStep10(row.get(i++));
                bean.setStep11(row.get(i++));
                bean.setStep12(row.get(i++));
                bean.setStep13(row.get(i++));
                bean.setStep14(row.get(i++));
                bean.setStep15(row.get(i++));
                bean.setStep16(row.get(i++));
                bean.setStep17(row.get(i++));
                bean.setStep18(row.get(i++));
                bean.setStep19(row.get(i++));
                bean.setStep20(row.get(i++));
                bean.setStep21(row.get(i++));
                bean.setStep22(row.get(i++));
                bean.setStep23(row.get(i++));
                bean.setStep24(row.get(i++));
                bean.setStep25(row.get(i++));
                bean.setStep26(row.get(i++));
                bean.setStep27(row.get(i++));
                bean.setStep28(row.get(i++));
                bean.setStep29(row.get(i++));
                bean.setStep30(row.get(i++));
                bean.setStep31(row.get(i++));
                bean.setStep32(row.get(i++));
                bean.setStep33(row.get(i++));
                bean.setStep34(row.get(i++));
                bean.setStep35(row.get(i++));

                data.add(bean);

                if (data.size() >= 1000) {
                    criticalBomCalculatorDao.insertReport3Data(batchId, data);
                    data.clear();
                }
            }
        }, new StylesTable());

        criticalBomCalculatorDao.insertReport3Data(batchId, data);
        return response;
    }

    @Override
    public Response uploadReport9(String batchId, MultipartFile file) throws Exception {
        File tempFile = File.createTempFile(String.valueOf(System.currentTimeMillis()), ".tmp");
        file.transferTo(tempFile);

        List<RecourseInput> data = new ArrayList<>();
        criticalBomCalculatorDao.deleteReport9Data(batchId);
        excelTemplate.read(tempFile, 1, new SimpleSheetContentsHandler() {
            @Override
            public void handleRow(int rowNum) {

                if (rowNum == 0) {
                    return;
                }

                int i = 0;
                RecourseInput bean = new RecourseInput();
                bean.setMaterial(row.get(i++));
                bean.setStep0(row.get(i++));

                data.add(bean);

                if (data.size() >= 1000) {
                    criticalBomCalculatorDao.insertReport9Data(batchId, data);
                    data.clear();
                }
            }
        }, new StylesTable());

        criticalBomCalculatorDao.insertReport9Data(batchId, data);
        return response;
    }

    @Override
    public Response uploadReport7(String batchId, MultipartFile file) throws Exception {
        File tempFile = File.createTempFile(String.valueOf(System.currentTimeMillis()), ".tmp");
        file.transferTo(tempFile);

        List<SubstitutionInput> data = new ArrayList<>();
        criticalBomCalculatorDao.deleteReport7Data(batchId);
        excelTemplate.read(tempFile, 1, new SimpleSheetContentsHandler() {
            @Override
            public void handleRow(int rowNum) {

                if (rowNum == 0) {
                    return;
                }

                int i = 0;
                SubstitutionInput bean = new SubstitutionInput();
                bean.setMaterialInBom(row.get(i++));
                bean.setFollowUpMaterial(row.get(i++));


                data.add(bean);

                if (data.size() >= 1000) {
                    criticalBomCalculatorDao.insertReport7Data(batchId, data);
                    data.clear();
                }
            }
        }, new StylesTable());

        criticalBomCalculatorDao.insertReport7Data(batchId, data);
        return response;
    }

    @Override
    public Response queryTaskQueue(Map<String, Object> parameterMap) {
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setTotal(criticalBomCalculatorDao.queryTaskQueueCount(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(criticalBomCalculatorDao.queryTaskQueue(parameterMap));
        }
        return response.setBody(page);
    }

    @Override
    public Response queryReport4(Map<String, Object> parameterMap) {
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        Map<String, Object> stepInfo = criticalBomCalculatorDao.queryBatchStepInfo(parameterMap);
        if (stepInfo == null || stepInfo.isEmpty() || stepInfo.get("STEP") == null) {
            return response.setBody(page);
        }
        parameterMap.put("steps", criticalBomCalculatorDao.queryBatchStepName(parameterMap));
        page.setTotal(criticalBomCalculatorDao.queryReport4Count(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(criticalBomCalculatorDao.queryReport4(parameterMap));
        }
        return response.setBody(page);
    }

    @Override
    public void downloadReport4(Map<String, Object> parameterMap, HttpServletResponse res) {
        parameterMap.put("steps", criticalBomCalculatorDao.queryBatchStepName(parameterMap));
        SimplePage<Map<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        String fileName = "demand_to_do_" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(res, fileName, "com.scp.simulation.dao.IcriticalBomCalculatorDao.queryReport4", parameterMap);
    }

    @Override
    public Response queryReport5(Map<String, Object> parameterMap) {
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setTotal(criticalBomCalculatorDao.queryReport5Count(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(criticalBomCalculatorDao.queryReport5(parameterMap));
        }
        return response.setBody(page);
    }

    @Override
    public void downloadReport5(Map<String, Object> parameterMap, HttpServletResponse res) {
        SimplePage<Map<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        String fileName = "recourse_shortage_" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(res, fileName, "com.scp.simulation.dao.IcriticalBomCalculatorDao.queryReport5", parameterMap);
    }

    @Override
    public Response queryReport10(Map<String, Object> parameterMap) {
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setTotal(criticalBomCalculatorDao.queryReport10Count(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(criticalBomCalculatorDao.queryReport10(parameterMap));
        }
        return response.setBody(page);
    }

    @Override
    public void downloadReport10(Map<String, Object> parameterMap, HttpServletResponse res) {
        SimplePage<Map<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        String fileName = "Output_Max" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(res, fileName, "com.scp.simulation.dao.IcriticalBomCalculatorDao.queryReport10", parameterMap);
    }

    @Override
    public Response queryReport8(Map<String, Object> parameterMap) {
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setTotal(criticalBomCalculatorDao.queryReport8Count(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(criticalBomCalculatorDao.queryReport8(parameterMap));
        }
        return response.setBody(page);
    }

    @Override
    public void downloadReport8(Map<String, Object> parameterMap, HttpServletResponse res) {
        SimplePage<Map<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        String fileName = "remain_recourse" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(res, fileName, "com.scp.simulation.dao.IcriticalBomCalculatorDao.queryReport8", parameterMap);
    }

    @Override
    public Response queryReport6(Map<String, Object> parameterMap) {
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setTotal(criticalBomCalculatorDao.queryReport6Count(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(criticalBomCalculatorDao.queryReport6(parameterMap));
        }
        return response.setBody(page);
    }

    @Override
    public void downloadReport6(Map<String, Object> parameterMap, HttpServletResponse res) {
        SimplePage<Map<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        String fileName = "rm_shortage_" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(res, fileName, "com.scp.simulation.dao.IcriticalBomCalculatorDao.queryReport6", parameterMap);
    }

    @Override
    public Response queryReport7(Map<String, Object> parameterMap) {
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setTotal(criticalBomCalculatorDao.queryReport7Count(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(criticalBomCalculatorDao.queryReport7(parameterMap));
        }
        return response.setBody(page);
    }

    @Override
    public void downloadReport7(Map<String, Object> parameterMap, HttpServletResponse res) {
        SimplePage<Map<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        String fileName = "component_substitution_" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(res, fileName, "com.scp.simulation.dao.IcriticalBomCalculatorDao.queryReport7", parameterMap);
    }

    @Override
    public Response saveReport7(Map<String, Object> parameterMap) {
        String batchId = (String) parameterMap.get("batchId");
        scpTableHelper.setScpTableInsertHandler((headers, creates) -> criticalBomCalculatorDao.createReport7ByTable(headers, creates, batchId));
        scpTableHelper.setScpTableDeleteHandler(deletes -> criticalBomCalculatorDao.deleteReport7ByTable(deletes, batchId));
        scpTableHelper.setScpTableUpdateHandler((pk, updates) -> criticalBomCalculatorDao.updateReport7ByTable(pk, updates, batchId));
        Message message = scpTableHelper.execCRUD(parameterMap);
        return response.setBody(message);
    }

    @Override
    public Response queryExecuteLogs(Map<String, Object> parameterMap) {
        return response.setBody(criticalBomCalculatorDao.queryExecuteLogs(parameterMap));
    }

    private void sendLog(String batchID, String userid, String message) {
        BestCanDoCalculatorLog log = new BestCanDoCalculatorLog();
        log.setBatch_id(batchID);
        log.setTime(DEFAULT_DATE_FORMAT.format(new Date()));
        log.setMessage(message);
        MqttConfiguration.publishMessage("scp/dss/ui/bcd/" + StringUtils.lowerCase(userid), log.toJSONString());
    }

    private void generateNewBatchCascaderFilter(Map<String, Object> parameterMap, String filterKey) {
        // 生成筛选条件
        JSONArray categoryArray = (JSONArray) parameterMap.get(filterKey);
        if (categoryArray != null) {
            Map<String, List<String>> filterMap = new HashMap<>();

            for (Object subObj : categoryArray) {
                JSONArray subArray = (JSONArray) subObj;
                String columnName = subArray.getString(0);

                String value = subArray.getString(1);
                String key = Utils.randomStr(8);

                if (Utils.hasInjectionAttack(columnName)) {
                    continue;
                }

                List<String> fl = filterMap.computeIfAbsent(columnName, k -> new ArrayList<>());
                if (Utils.containsStr("BASIC_START_DATE,CALENDAR_DATE", columnName)) {
                    fl.add("to_date(#{" + key + ",jdbcType=VARCHAR}, 'yyyy/mm/dd')");
                } else {
                    fl.add("#{" + key + ",jdbcType=VARCHAR}");
                }
                parameterMap.put(key, value);
            }

            List<String> filterList = new ArrayList<>();

            for (String key : filterMap.keySet()) {
                List<String> fl = filterMap.get(key);
                filterList.add("t." + key + " in (" + StringUtils.join(fl, ",") + ")");
            }

            if (filterList.isEmpty()) {
                parameterMap.put(filterKey + "Filters", " 1 = 0 ");
            } else {
                parameterMap.put(filterKey + "Filters", StringUtils.join(filterList, " and "));
            }
        }
    }

    private String getColumnName(Object labelObj) {
        String label = (String) labelObj;
        if (label == null) {
            return null;
        }
        if (Utils.hasInjectionAttack(label)) {
            return "";
        }
        return label;
    }

    private void getCompareTasks(Map<String, Object> parameterMap) {
        List<String> selectedxAxis = ((JSONArray) parameterMap.get("selectedxAxis")).toJavaList(String.class);
        List<String> getCompareTaskL = ((JSONArray) parameterMap.get("compareTaskL")).toJavaList(String.class);
        List<String> getCompareTaskR = ((JSONArray) parameterMap.get("compareTaskR")).toJavaList(String.class);

        parameterMap.put("selectedxAxis", selectedxAxis);
        String compareTaskL = getCompareTaskL.get(getCompareTaskL.size() - 1).split("@")[1];   //获取前端选择的compareTaskL
        parameterMap.put("compareTaskL", compareTaskL);
        if (getCompareTaskR != null && getCompareTaskR.size() != 0) {
            String compareTaskR = getCompareTaskR.get(getCompareTaskR.size() - 1).split("@")[1];   //获取前端选择的compareTaskR
            parameterMap.put("compareTaskR", compareTaskR);
        } else {
            parameterMap.put("compareTaskR", "");
        }
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response compareReport1(Map<String, Object> parameterMap) {
        this.getCompareTasks(parameterMap);
        parameterMap.put("compare1XAxisType", parameterMap.get("compare1XAxisType"));

        LinkedHashMap<String, Object> resultMap = new LinkedHashMap<>();
        List<BestCanDoCompare1> dataList;
        Map<String, BigDecimal> dataMap = new HashMap<>();
        Map<String, String> xAxisMap = new HashMap<>();

        String compare1Value = (String) parameterMap.get("compareValue");

        if ("QTY".equalsIgnoreCase(compare1Value)) {
            compare1Value = "SUM(qty)";
        } else if ("VALUE".equalsIgnoreCase(compare1Value)) {
            compare1Value = "SUM(VALUE)";
        } else {
            compare1Value = "COUNT(1)";
        }
        parameterMap.put("compare1Value", compare1Value);

        List<String> legend = criticalBomCalculatorDao.compareReport1legend(parameterMap);
        dataList = criticalBomCalculatorDao.compareReport1(parameterMap);

        for (BestCanDoCompare1 data : dataList) {
            dataMap.put(data.getKey(), data.getVALUE());
            xAxisMap.put(data.getHORIZONTAL(), "");
        }
        List<String> xAxisList = xAxisMap.keySet().stream().sorted(String::compareTo).collect(Collectors.toList());  //keySet()返回的是map对象的key值的set集合

        for (String l : legend) {
            List<BigDecimal> temp = new ArrayList<>();

            for (String x : xAxisList) {
                temp.add(dataMap.getOrDefault(l + "#" + x, BigDecimal.ZERO));
            }
            resultMap.put(l, temp);
        }
        resultMap.put("xAxis", xAxisList);

        return response.setBody(resultMap);
    }

    @Override
    public Response compareReport1Details(Map<String, Object> parameterMap) {
        this.getCompareTasks(parameterMap);
        String compare1SelectedName = (String) parameterMap.get("compare1SelectedName");
        String compare1SelectedSeriesName = (String) parameterMap.get("compare1SelectedSeriesName");
        parameterMap.put("compare1SelectedName", compare1SelectedName);
        parameterMap.put("compare1SelectedSeriesName", compare1SelectedSeriesName);
        parameterMap.put("compare1XAxisType", parameterMap.get("compare1XAxisType"));

        if (compare1SelectedName.startsWith("L_")) {
            parameterMap.put("isLeftTask", true);
        } else if (compare1SelectedName.startsWith("R_")) {
            parameterMap.put("isLeftTask", false);
        }
        String compare1SelectedValue = (String) parameterMap.get("compare1SelectedValue");
        parameterMap.put("compare3SelectedValue", compare1SelectedValue);

        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setTotal(criticalBomCalculatorDao.compareReport1DetailsCount(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(criticalBomCalculatorDao.compareReport1Details(parameterMap));
        }
        return response.setBody(page);
    }

    @Override
    public void downloadCompareReport1Details(Map<String, Object> parameterMap, HttpServletResponse res) {
        this.getCompareTasks(parameterMap);
        String compare1SelectedName = (String) parameterMap.get("compare1SelectedName");
        String compare1SelectedSeriesName = (String) parameterMap.get("compare1SelectedSeriesName");
        parameterMap.put("compare1SelectedName", compare1SelectedName);
        parameterMap.put("compare1SelectedSeriesName", compare1SelectedSeriesName);
        parameterMap.put("compare1XAxisType", parameterMap.get("compare1XAxisType"));

        if (compare1SelectedName.startsWith("L_")) {
            parameterMap.put("isLeftTask", true);
        } else if (compare1SelectedName.startsWith("R_")) {
            parameterMap.put("isLeftTask", false);
        }
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        String fileName = "bcd_optimization_result_" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(res, fileName, "com.scp.simulation.dao.IcriticalBomCalculatorDao.compareReport1Details", parameterMap);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response compareReport2(Map<String, Object> parameterMap) throws Exception {
        this.getCompareTasks(parameterMap);
        parameterMap.put("compare2XAxisType", parameterMap.get("compare2XAxisType"));

        // 将前台传过来的label转换成列名, 同时也可以防止恶意代码注入
        parameterMap.put("level1", this.getColumnName(parameterMap.get("level1")));
        parameterMap.put("level2", this.getColumnName(parameterMap.get("level2")));

        String compare2Value = (String) parameterMap.get("compareValue");

        if ("QTY".equalsIgnoreCase(compare2Value)) {
            compare2Value = "SUM(SHORTAGE)";
        } else if ("VALUE".equalsIgnoreCase(compare2Value)) {
            compare2Value = "SUM(SHORTAGE * UNIT_COST)";
        } else {
            compare2Value = "COUNT(1)";
        }
        parameterMap.put("compare2Value", compare2Value);

        List<BestCanDoTreemap> resultList = new ArrayList<>();
        List<BestCanDoCompare2Bean> dataList = criticalBomCalculatorDao.compareReport2(parameterMap);
        for (BestCanDoCompare2Bean data : dataList) {
            this.convertReport2Data(resultList, data);
        }
        return response.setBody(resultList);
    }

    @Override
    public Response compareReport2Details(Map<String, Object> parameterMap) {
        this.getCompareTasks(parameterMap);
        String compare2SelectedName = (String) parameterMap.get("compare2SelectedName");
        String compare2SelectedSeriesName = (String) parameterMap.get("compare2SelectedSeriesName");
        parameterMap.put("compare2SelectedName", compare2SelectedName);
        parameterMap.put("compare2SelectedSeriesName", compare2SelectedSeriesName);
        parameterMap.put("compare2XAxisType", parameterMap.get("compare2XAxisType"));

        if ("LEVEL_A".equalsIgnoreCase(compare2SelectedName)) {
            parameterMap.put("isLeftTask", true);
        } else if ("LEVEL_B".equalsIgnoreCase(compare2SelectedName)) {
            parameterMap.put("isLeftTask", false);
        }

        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setTotal(criticalBomCalculatorDao.compareReport2DetailsCount(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(criticalBomCalculatorDao.compareReport2Details(parameterMap));
        }
        return response.setBody(page);
    }

    @Override
    public void downloadCompareReport2Details(Map<String, Object> parameterMap, HttpServletResponse res) {
        this.getCompareTasks(parameterMap);
        String compare2SelectedName = (String) parameterMap.get("compare2SelectedName");
        String compare2SelectedSeriesName = (String) parameterMap.get("compare2SelectedSeriesName");
        parameterMap.put("compare2SelectedName", compare2SelectedName);
        parameterMap.put("compare2SelectedSeriesName", compare2SelectedSeriesName);
        parameterMap.put("compare2XAxisType", parameterMap.get("compare2XAxisType"));

        if ("LEVEL_A".equalsIgnoreCase(compare2SelectedName)) {
            parameterMap.put("isLeftTask", true);
        } else if ("LEVEL_B".equalsIgnoreCase(compare2SelectedName)) {
            parameterMap.put("isLeftTask", false);
        }
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        String fileName = "bcd_global_shortage_summary" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(res, fileName, "com.scp.simulation.dao.IcriticalBomCalculatorDao.compareReport2Details", parameterMap);
    }

    /**
     * 将列表转化为Tree数据
     *
     * @param list 输出树
     * @param data 输入值
     * @throws Exception 异常
     */
    private void convertReport2Data(List<BestCanDoTreemap> list, BestCanDoCompare2Bean data) throws Exception {
        String[] categorysOrg = new String[]{data.getCategory1(), data.getCategory2()};
        List<String> categories = new ArrayList<>();

        for (String category : categorysOrg) {
            if (StringUtils.isNotBlank(category)) {
                categories.add(category);
            } else {
                break;
            }
        }
        // 这边逻辑比较复杂, 所以用最笨的方法来描述了, 以免后期不好维护
        // 先把这一行数据转成treemap的数据
        // 第一个节点
        List<BestCanDoTreemap> child = new ArrayList<>();
        BestCanDoTreemap root = new BestCanDoTreemap();
        root.setName(categories.get(0));
        root.setTips(data.copyTooltips()); // 因为这个tooltips要放在树中全局使用, 所以必须要生成一个新节点
        root.setChildren(child);

        // 中间节点
        for (int i = 1; i < categories.size() - 1; i++) {
            BestCanDoTreemap treemap = new BestCanDoTreemap();
            treemap.setName(categories.get(i));
            treemap.setTips(data.copyTooltips());

            child.add(treemap);
            child = new ArrayList<>();
            treemap.setChildren(child);
        }

        // 最后一个节点
        BestCanDoTreemap lastNode = new BestCanDoTreemap();
        lastNode.setName(categories.get(categories.size() - 1));
        lastNode.setValue(data.getValue());
        lastNode.setTips(data.copyTooltips());
        child.add(lastNode);

        // 将这行treemap与原始数据相加
        // 先找到list中是否有这个数据节点
        Optional<BestCanDoTreemap> beanOpt = list.stream().filter(b -> b.getName().equals(categories.get(0))).findFirst();
        if (beanOpt.isPresent()) {
            BestCanDoTreemap bean = beanOpt.get();
            bean.add(root); // 两个节点合并
        } else { //找不到的时候最省事, 直接放入list就可以了
            list.add(root);
        }
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response compareReport3(Map<String, Object> parameterMap) {
        this.getCompareTasks(parameterMap);

        parameterMap.put("compare3XAxisType", parameterMap.get("compare3XAxisType"));
        String compare3Value = (String) parameterMap.get("compareValue");

        if ("QTY".equalsIgnoreCase(compare3Value)) {
            compare3Value = "SUM(RM_SHORTAGE)";
        } else if ("VALUE".equalsIgnoreCase(compare3Value)) {
            compare3Value = "SUM(UNIT_COST * RM_SHORTAGE)";
        } else {
            compare3Value = "COUNT(1)";
        }
        parameterMap.put("compare3Value", compare3Value);

        LinkedHashMap<String, Object> resultMap = new LinkedHashMap<>();
        List<BestCanDoCompare1> dataList;
        Map<String, BigDecimal> dataMap = new HashMap<>();
        Map<String, String> xAxisMap = new HashMap<>();

        List<String> legend = criticalBomCalculatorDao.compareReport3legend(parameterMap);
        dataList = criticalBomCalculatorDao.compareReport3(parameterMap);

        for (BestCanDoCompare1 data : dataList) {
            dataMap.put(data.getKey(), data.getVALUE());
            xAxisMap.put(data.getHORIZONTAL(), "");
        }
        List<String> xAxisList = xAxisMap.keySet().stream().sorted(String::compareTo).collect(Collectors.toList());  //keySet()返回的是map对象的key值的set集合

        for (String l : legend) {
            List<BigDecimal> temp = new ArrayList<>();

            for (String x : xAxisList) {
                temp.add(dataMap.getOrDefault(l + "#" + x, BigDecimal.ZERO));
            }
            resultMap.put(l, temp);
        }
        resultMap.put("xAxis", xAxisList);

        return response.setBody(resultMap);
    }


    @Override
    public Response compareReport3Details(Map<String, Object> parameterMap) {
        this.getCompareTasks(parameterMap);
        String compare3SelectedName = (String) parameterMap.get("compare3SelectedName");
        String compare3SelectedSeriesName = (String) parameterMap.get("compare3SelectedSeriesName");
        parameterMap.put("compare3SelectedName", compare3SelectedName);
        parameterMap.put("compare3SelectedSeriesName", compare3SelectedSeriesName);
        parameterMap.put("compare3XAxisType", parameterMap.get("compare3XAxisType"));

        if (compare3SelectedName == null){
            compare3SelectedName = "L_TITLE";
        }

        if (compare3SelectedName.startsWith("L_")) {
            parameterMap.put("isLeftTask", true);
        } else if (compare3SelectedName.startsWith("R_")) {
            parameterMap.put("isLeftTask", false);
        }

        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setTotal(criticalBomCalculatorDao.compareReport3DetailsCount(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(criticalBomCalculatorDao.compareReport3Details(parameterMap));
        }
        return response.setBody(page);
    }

    @Override
    public void downloadCompareReport3Details(Map<String, Object> parameterMap, HttpServletResponse res) {
        this.getCompareTasks(parameterMap);
        String compare3SelectedName = (String) parameterMap.get("compare3SelectedName");
        String compare3SelectedSeriesName = (String) parameterMap.get("compare3SelectedSeriesName");
        parameterMap.put("compare3SelectedName", compare3SelectedName);
        parameterMap.put("compare3SelectedSeriesName", compare3SelectedSeriesName);
        parameterMap.put("compare3XAxisType", parameterMap.get("compare3XAxisType"));

        if (compare3SelectedName == null){
            compare3SelectedName = "L_TITLE";
        }

        if (compare3SelectedName.startsWith("L_")) {
            parameterMap.put("isLeftTask", true);
        } else if (compare3SelectedName.startsWith("R_")) {
            parameterMap.put("isLeftTask", false);
        }
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        String fileName = "bcd_shortage_analysis_" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(res, fileName, "com.scp.simulation.dao.IcriticalBomCalculatorDao.compareReport3Details", parameterMap);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response compareReport4Left(Map<String, Object> parameterMap) {
        this.getCompareTasks(parameterMap);

        LinkedHashMap<String, Object> resultMap = new LinkedHashMap<>();
        List<BestCanDoCompare1> dataList;
        Map<String, BigDecimal> dataMap = new HashMap<>();
        Map<String, String> xAxisMap = new HashMap<>();

        String compare4Value = (String) parameterMap.get("compareValue");

        if ("QTY".equalsIgnoreCase(compare4Value)) {
            compare4Value = "SUM(VALUE)";
        } else if ("VALUE".equalsIgnoreCase(compare4Value)) {
            compare4Value = "SUM(UNIT_COST * VALUE)";
        } else {
            compare4Value = "COUNT(1)";
        }
        parameterMap.put("compare4Value", compare4Value);

        List<String> legend = criticalBomCalculatorDao.compareReport4legend(parameterMap);
        dataList = criticalBomCalculatorDao.compareReport4Left(parameterMap);

        for (BestCanDoCompare1 data : dataList) {
            dataMap.put(data.getKey(), data.getVALUE());
            xAxisMap.put(data.getHORIZONTAL(), "");
        }
        List<String> xAxisList = xAxisMap.keySet().stream().sorted(String::compareTo).collect(Collectors.toList());  //keySet()返回的是map对象的key值的set集合

        for (String l : legend) {
            List<BigDecimal> temp = new ArrayList<>();

            for (String x : xAxisList) {
                temp.add(dataMap.getOrDefault(l + "#" + x, BigDecimal.ZERO));
            }
            resultMap.put(l, temp);
        }
        resultMap.put("xAxis", xAxisList);

        return response.setBody(resultMap);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response compareReport4Right(Map<String, Object> parameterMap) {
        this.getCompareTasks(parameterMap);

        LinkedHashMap<String, Object> resultMap = new LinkedHashMap<>();
        List<BestCanDoCompare1> dataList;
        Map<String, BigDecimal> dataMap = new HashMap<>();
        Map<String, String> xAxisMap = new HashMap<>();

        String compare4Value = (String) parameterMap.get("compareValue");

        if ("QTY".equalsIgnoreCase(compare4Value)) {
            compare4Value = "SUM(VALUE)";
        } else if ("VALUE".equalsIgnoreCase(compare4Value)) {
            compare4Value = "SUM(UNIT_COST * VALUE)";
        } else {
            compare4Value = "COUNT(1)";
        }
        parameterMap.put("compare4Value", compare4Value);

        List<String> legend = criticalBomCalculatorDao.compareReport4legend(parameterMap);
        dataList = criticalBomCalculatorDao.compareReport4Right(parameterMap);

        for (BestCanDoCompare1 data : dataList) {
            dataMap.put(data.getKey(), data.getVALUE());
            xAxisMap.put(data.getHORIZONTAL(), "");
        }
        List<String> xAxisList = xAxisMap.keySet().stream().sorted(String::compareTo).collect(Collectors.toList());  //keySet()返回的是map对象的key值的set集合


        for (String l : legend) {
            List<BigDecimal> temp = new ArrayList<>();

            for (String x : xAxisList) {
                temp.add(dataMap.getOrDefault(l + "#" + x, BigDecimal.ZERO));
            }
            resultMap.put(l, temp);
        }
        resultMap.put("xAxis", xAxisList);

        return response.setBody(resultMap);
    }

    @Override
    public Response compareReport4Details(Map<String, Object> parameterMap) {
        this.getCompareTasks(parameterMap);
        String compare4SelectedName = (String) parameterMap.get("compare4SelectedName");
        parameterMap.put("compare4SelectedName", compare4SelectedName);

        List<String> getCompareTask = ((JSONArray) parameterMap.get("compare4SelectedTaskId")).toJavaList(String.class);
        String compareTask = getCompareTask.get(getCompareTask.size() - 1).split("@")[1];   //获取前端选择的compareTaskL
        parameterMap.put("compareTask", compareTask);

        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setTotal(criticalBomCalculatorDao.compareReport4DetailsCount(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(criticalBomCalculatorDao.compareReport4Details(parameterMap));
        }
        return response.setBody(page);
    }

    @Override
    public void downloadCompareReport4Details(Map<String, Object> parameterMap, HttpServletResponse res) {
        this.getCompareTasks(parameterMap);
        String compare4SelectedName = (String) parameterMap.get("compare4SelectedName");
        parameterMap.put("compare4SelectedName", compare4SelectedName);

        List<String> getCompareTask = ((JSONArray) parameterMap.get("compare4SelectedTaskId")).toJavaList(String.class);
        String compareTask = getCompareTask.get(getCompareTask.size() - 1).split("@")[1];   //获取前端选择的compareTaskL
        parameterMap.put("compareTask", compareTask);
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        String fileName = "bcd_soh_evolution_" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(res, fileName, "com.scp.simulation.dao.IcriticalBomCalculatorDao.compareReport4Details", parameterMap);
    }

    @Override
    public Response compareReportOverview(Map<String, Object> parameterMap) {
        String compareOverviewValue = (String) parameterMap.get("compareValue");

        if ("QTY".equalsIgnoreCase(compareOverviewValue)) {
            compareOverviewValue = "SUM(QTY)";
        } else if ("VALUE".equalsIgnoreCase(compareOverviewValue)) {
            compareOverviewValue = "SUM(VALUE)";
        } else {
            compareOverviewValue = "COUNT(1)";
        }
        parameterMap.put("compareOverviewValue", compareOverviewValue);
        this.getCompareTasks(parameterMap);
        return response.setBody(criticalBomCalculatorDao.compareReportOverview(parameterMap));
    }

    @Override
    public Response compareOverviewDetails(Map<String, Object> parameterMap) {
        this.getCompareTasks(parameterMap);

        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setTotal(criticalBomCalculatorDao.compareOverviewDetailsCount(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(criticalBomCalculatorDao.compareOverviewDetails(parameterMap));
        }
        return response.setBody(page);
    }

    public void downloadCompareOverviewDetails(Map<String, Object> parameterMap, HttpServletResponse response) {
        this.getCompareTasks(parameterMap);
        String compareOverviewDetailsType = (String) parameterMap.get("compareOverviewDetailsType");
        SimplePage<Map<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);

        String fileName = "compare_overview_" + compareOverviewDetailsType.toLowerCase() + "_" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(response, fileName, "com.scp.simulation.dao.IcriticalBomCalculatorDao.compareOverviewDetails", parameterMap);
    }
    @Override
    @SuppressWarnings("unchecked")
    public Response shareCondition(String userid, String username, String email, Map<String, Object> parameterMap) {
        List<String> users = (List<String>) parameterMap.get("users");
        String batch_id = (String) parameterMap.get("batch_id");
        if (users == null || users.size() == 0) {
            return response.setBody("Please select at least one user to share!");
        }
        parameterMap.put("userid", userid);
        parameterMap.put("batch_id", batch_id);

        criticalBomCalculatorDao.shareCondition(parameterMap);


        // send notice mail
        StringBuilder body = new StringBuilder();
        String remarks = (String) parameterMap.get("remarks");
        String name = (String) parameterMap.get("name");
        MailBean mailBean = new MailBean();
        mailBean.setSubject("【Best can do Sharing】" + username + " shared a calculator result [" + name + "] with you");
        List<String> to = new ArrayList<>();
        for (String user : users) {
            to.add(user + "@se.com");
        }
        mailBean.setTo(StringUtils.join(to, ","));
        mailBean.setCc(email);

        body.append("<div style='font-size:10pt;font-family:DengXian;'>");
        body.append("<p>");
        body.append(username).append(" shared a calculator result [").append(name).append("] with you");
        body.append("</p>");
        if (StringUtils.isNotBlank(remarks)) {
            body.append("<br/>");
            body.append(remarks);
            body.append("<br/>");
        }

        body.append("Click <a href=https://scp-dss.cn.schneider-electric.com/#/simulation/best_can_do_calculator");
        body.append("><b><i>");
        body.append("Best Can Do Calculator");
        body.append("</i></b></a> for more information</div>");
        String style = "<style>p{font-size: 10pt;font-family:DengXian;padding:0;margin:0} span{font-size: 10pt;font-family:DengXian;} div{font-size: 10pt;font-family:DengXian;}</style>";

        String signatrue = systemService.getMailSignature(userid);
        if (StringUtils.isBlank(signatrue)) {
            signatrue = "";
        }
        signatrue = "<br><br><br>" + signatrue;
        mailBean.setBody(style + body + signatrue);
        mailFeignClient.sendAsync(mailBean);

        return response;
    }


}
