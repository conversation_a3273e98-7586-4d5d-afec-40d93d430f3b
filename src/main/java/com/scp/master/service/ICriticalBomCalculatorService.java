package com.scp.master.service;

import com.starter.context.bean.Response;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;

import java.util.Map;

public interface ICriticalBomCalculatorService {

    Response initPage(String userid);

    Response saveNewBatch(Map<String, Object> parameterMap, String userid);

    Response queryStart(Map<String, Object> parameterMap);

    Response queryReport1(Map<String, Object> parameterMap);

    Response queryReport2(Map<String, Object> parameterMap);

    Response queryReport11(Map<String, Object> parameterMap);

    Response queryReport3(Map<String, Object> parameterMap);

    Response queryReport9(Map<String, Object> parameterMap);

    Response queryExecuteLogs(Map<String, Object> parameterMap);

    Response queryBatchList(String userid);

    Response deleteBatch(Map<String, Object> parameterMap, String userid);

    Response sendSimulateRequest(Map<String, Object> parameterMap, String userid);

    void downloadStart(Map<String, Object> parameterMap, HttpServletResponse response);

    void downloadReport1(Map<String, Object> parameterMap, HttpServletResponse response);

    void downloadReport2(Map<String, Object> parameterMap, HttpServletResponse response);

    void downloadReport11(Map<String, Object> parameterMap, HttpServletResponse response);

    void downloadReport3(Map<String, Object> parameterMap, HttpServletResponse response);

    void downloadReport9(Map<String, Object> parameterMap, HttpServletResponse response);

    Response saveStart(Map<String, Object> parameterMap);

    Response saveReport1(Map<String, Object> parameterMap);

    Response saveReport2(Map<String, Object> parameterMap);

    Response saveReport11(Map<String, Object> parameterMap);

    Response saveReport3(Map<String, Object> parameterMap);

    Response saveReport9(Map<String, Object> parameterMap);

    Response saveReport7(Map<String, Object> parameterMap);

    void downloadStartTemplate(Map<String, Object> parameterMap, HttpServletResponse response);

    void downloadReport1Template(Map<String, Object> parameterMap, HttpServletResponse response);

    void downloadReport2Template(Map<String, Object> parameterMap, HttpServletResponse response);

    void downloadReport11Template(Map<String, Object> parameterMap, HttpServletResponse response);

    void downloadReport3Template(Map<String, Object> parameterMap, HttpServletResponse response);

    void downloadReport9Template(Map<String, Object> parameterMap, HttpServletResponse response);

    void downloadReport7Template(Map<String, Object> parameterMap, HttpServletResponse response);

    Response uploadStart(String batchId, MultipartFile file) throws Exception;

    Response uploadReport1(String batchId, MultipartFile file) throws Exception;

    Response uploadReport2(String batchId, MultipartFile file) throws Exception;

    Response uploadReport11(String batchId, MultipartFile file) throws Exception;


    Response uploadReport3(String batchId, MultipartFile file) throws Exception;

    Response uploadReport9(String batchId, MultipartFile file) throws Exception;

    Response uploadReport7(String batchId, MultipartFile file) throws Exception;

    Response queryTaskQueue(Map<String, Object> parameterMap);

    Response queryReport4(Map<String, Object> parameterMap);

    Response queryReport5(Map<String, Object> parameterMap);

    Response queryReport10(Map<String, Object> parameterMap);

    Response queryReport8(Map<String, Object> parameterMap);

    Response queryReport6(Map<String, Object> parameterMap);

    Response queryReport7(Map<String, Object> parameterMap);

    void downloadReport4(Map<String, Object> parameterMap, HttpServletResponse response);

    void downloadReport5(Map<String, Object> parameterMap, HttpServletResponse response);

    void downloadReport10(Map<String, Object> parameterMap, HttpServletResponse response);

    void downloadReport8(Map<String, Object> parameterMap, HttpServletResponse response);

    void downloadReport6(Map<String, Object> parameterMap, HttpServletResponse response);

    void downloadReport7(Map<String, Object> parameterMap, HttpServletResponse response);

    Response queryBatchInfo(Map<String, Object> parameterMap);

    Response initExistsGroup(String userid);

    Response queryTaskInfo(String userid);

    Response compareReport1(Map<String, Object> parameterMap);

    Response compareReport1Details(Map<String, Object> parameterMap);

    void downloadCompareReport1Details(Map<String, Object> parameterMap, HttpServletResponse response);

    Response compareReport2(Map<String, Object> parameterMap) throws Exception;

    Response compareReport2Details(Map<String, Object> parameterMap);

    void downloadCompareReport2Details(Map<String, Object> parameterMap, HttpServletResponse response);


    Response compareReport3(Map<String, Object> parameterMap);

    Response compareReport3Details(Map<String, Object> parameterMap);

    void downloadCompareReport3Details(Map<String, Object> parameterMap, HttpServletResponse response);

    Response compareReport4Left(Map<String, Object> parameterMap);

    Response compareReport4Right(Map<String, Object> parameterMap);

    Response compareReport4Details(Map<String, Object> parameterMap);

    void downloadCompareReport4Details(Map<String, Object> parameterMap, HttpServletResponse response);

    Response compareReportOverview(Map<String, Object> parameterMap);

    Response compareOverviewDetails(Map<String, Object> parameterMap);

    void downloadCompareOverviewDetails(Map<String, Object> parameterMap, HttpServletResponse response);

    Response shareCondition(String userid, String username, String email, Map<String, Object> parameterMap);

}
