<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.scp.master.dao.ICriticalBomClickHouseDao">

	<select id="queryAvailablePlants" resultType="java.lang.String">
		SELECT T.PLANT_CODE FROM MR3_PLANT_MASTER_DATA T ORDER BY T.PLANT_CODE
	</select>

	<select id="queryCascader" resultType="java.util.Map">
		SELECT *
		FROM SCPA.MATERIAL_MASTER_FILTER_V T
		ORDER BY CATEGORY, multiIf(name = 'Others', 'zzz', name)
	</select>

	<select id="queryApplicationAuth" resultType="java.lang.Integer">
		select count(1)
		from SY_MENU_AUTH
		where MENU_CODE = 'menu270'
		  and USER_ID = #{userid, jdbcType=VARCHAR}
		  and upper(AUTH_DETAILS) = 'ADMIN'
	</select>

	<sql id="queryReport1Sql">
		<choose>
			<when test="selectType == 'UP'">
				WITH RECURSIVE bom_tree AS (
				SELECT BOM_COMPONENT                                                         AS ROOT_MATERIAL,
				MATERIAL                                                              AS PARENT_MATERIAL,
				BOM_COMPONENT                                                         AS CHILD_MATERIAL,
				CONCAT(MATERIAL, '->',BOM_COMPONENT )                                 AS ROUTE,
				CONCAT(PLANT_CODE, '->', PLANT_CODE)                                  AS PLANT_ROUTE,
				CONCAT('1', '->', COMPONENT_QTY)                                      AS COMPONENT_QTY_ROUTE,
				MATERIAL                                                              AS TOP,
				PLANT_CODE                                                            AS TOP_PLANT_CODE,
				BOM_COMPONENT                                                         AS BOTTOM,
				PLANT_CODE                                                            AS BOTTOM_PLANT_CODE,
				CASE
				WHEN PO_MATERIAL !=''
				THEN CONCAT(BOM_COMPONENT, '&lt;->', PO_MATERIAL)
				ELSE '' END                                                       AS TRANSFER_NUMBER,
				CONCAT(MATERIAL_PLANT_TYPE, '->', COMPONENT_PLANT_TYPE)               AS PLANT_TYPE_ROUTE,
				CONCAT(MATERIAL_CLUSTER_NAME, '->', COMPONENT_CLUSTER_NAME)           AS CLUSTER_NAME_ROUTE,
				CONCAT(MATERIAL_ENTITY, '->', COMPONENT_ENTITY)                       AS ENTITY_ROUTE,
				CONCAT(MATERIAL_PRODUCT_LINE, '->', COMPONENT_PRODUCT_LINE)           AS PRODUCT_LINE_ROUTE,
				CONCAT(MATERIAL_PRODUCTION_LINE, '->', T.COMPONENT_PRODUCTION_LINE)   AS PRODUCTION_LINE_ROUTE,
				CONCAT(MATERIAL_LOCAL_PRODUCT_LINE, '->',
				T.COMPONENT_LOCAL_PRODUCT_LINE)                                AS LOCAL_PRODUCT_LINE_ROUTE,
				CONCAT(MATERIAL_LOCAL_PRODUCT_FAMILY, '->',
				T.COMPONENT_LOCAL_PRODUCT_FAMILY)                              AS LOCAL_PRODUCT_FAMILY_ROUTE,
				CONCAT(MATERIAL_LOCAL_PRODUCT_SUBFAMILY, '->',
				T.COMPONENT_LOCAL_PRODUCT_SUBFAMILY)                           AS LOCAL_PRODUCT_SUBFAMILY_ROUTE,
				CONCAT(MATERIAL_HYPER_CARE_LEVEL, '->', T.COMPONENT_HYPER_CARE_LEVEL) AS HYPER_CARE_LEVEL_ROUTE,
				CONCAT(MATERIAL_MRP_CONTROLLER, '->', T.COMPONENT_MRP_CONTROLLER)     AS MRP_CONTROLLER_ROUTE,
				CONCAT(MATERIAL_STOCKING_POLICY, '->', T.COMPONENT_STOCKING_POLICY)   AS STOCKING_POLICY_ROUTE,
				CONCAT(MATERIAL_VENDOR_CODE, '->', T.COMPONENT_VENDOR_CODE)           AS VENDOR_CODE_ROUTE,
				CONCAT(MATERIAL_VENDOR_NAME, '->', T.COMPONENT_VENDOR_NAME)           AS VENDOR_NAME_ROUTE,
				CONCAT(MATERIAL_VENDOR_SHORT_NAME, '->',
				T.COMPONENT_VENDOR_SHORT_NAME)                                 AS VENDOR_SHORT_NAME_ROUTE,
				CONCAT(MATERIAL_VENDOR_FULL_NAME, '->', T.COMPONENT_VENDOR_FULL_NAME) AS VENDOR_FULL_NAME_ROUTE,
				CONCAT(MATERIAL_VENDOR_PARENT_NAME, '->',
				T.COMPONENT_VENDOR_PARENT_NAME)                                AS VENDOR_PARENT_NAME_ROUTE,
				CONCAT(MATERIAL_VENDOR_PARENT_CODE, '->',
				T.COMPONENT_VENDOR_PARENT_CODE)                                AS VENDOR_PARENT_CODE_ROUTE,
				CONCAT(MATERIAL_VENDOR_MATERIAL, '->', T.COMPONENT_VENDOR_MATERIAL)   AS VENDOR_MATERIAL_ROUTE,
				CONCAT(MATERIAL_SOURCE_CATEGORY, '->', T.COMPONENT_SOURCE_CATEGORY)   AS SOURCE_CATEGORY_ROUTE,
				CONCAT(MATERIAL_PLANNED_DELIV_TIME, '->',
				T.COMPONENT_PLANNED_DELIV_TIME)                                AS PLANNED_DELIV_TIME_ROUTE,
				CONCAT(MATERIAL_LT_RANGE, '->', T.COMPONENT_LT_RANGE)                 AS LT_RANGE_ROUTE,
				CONCAT(MATERIAL_UNIT_COST, '->', T.COMPONENT_UNIT_COST)               AS UNIT_COST_ROUTE,
				CONCAT(MATERIAL_SAFETY_STOCK, '->', T.COMPONENT_SAFETY_STOCK)         AS SAFETY_STOCK_ROUTE,
				CONCAT(MATERIAL_AMU_ONE_MM, '->', T.COMPONENT_AMU_ONE_MM)             AS AMU_ONE_MM_ROUTE,
				CONCAT(MATERIAL_AVG_MONTHLY_ORDER_INTAKE, '->',
				T.COMPONENT_AVG_MONTHLY_ORDER_INTAKE)                          AS AVG_MONTHLY_ORDER_INTAKE_ROUTE,
				CONCAT(MATERIAL_AMF_ONE_MM, '->', T.COMPONENT_AMF_ONE_MM)             AS AMF_ONE_MM_ROUTE,
				CONCAT(MATERIAL_COV, '->', T.COMPONENT_COV)                           AS COV_ROUTE,
				CONCAT(MATERIAL_SS2, '->', T.COMPONENT_SS2)                           AS SS2_ROUTE,
				CONCAT(MATERIAL_SS3, '->', T.COMPONENT_SS3)                           AS SS3_ROUTE,
				CONCAT(MATERIAL_CALCULATED_ABC, '->', T.COMPONENT_CALCULATED_ABC)     AS CALCULATED_ABC_ROUTE,
				CONCAT(MATERIAL_CALCULATED_FMR, '->', T.COMPONENT_CALCULATED_FMR)     AS CALCULATED_FMR_ROUTE,
				CONCAT(MATERIAL_NEW_PRODUCTS, '->', T.COMPONENT_NEW_PRODUCTS)         AS NEW_PRODUCTS_ROUTE,
				CONCAT(MATERIAL_PROCUREMENT_TYPE, '->', T.COMPONENT_PROCUREMENT_TYPE) AS PROCUREMENT_TYPE_ROUTE,
				CONCAT(MATERIAL_FOLLOW_UP_MATERIAL, '->',
				T.COMPONENT_FOLLOW_UP_MATERIAL)                                AS FOLLOW_UP_MATERIAL_ROUTE,
				CONCAT(MATERIAL_PRODN_SUPERVISOR, '->', T.COMPONENT_PRODN_SUPERVISOR) AS PRODN_SUPERVISOR_ROUTE,
				CONCAT(MATERIAL_IN_HOUSE_PRODN_LT, '->',
				T.COMPONENT_IN_HOUSE_PRODN_LT)                                 AS IN_HOUSE_PRODN_LT_ROUTE,
				CONCAT(MATERIAL_STARS_CODE, '->', T.COMPONENT_STARS_CODE)             AS STARS_CODE_ROUTE,
				CONCAT(MATERIAL_LAST_SO_SALES_DATE, '->',
				T.COMPONENT_LAST_SO_SALES_DATE)                                AS LAST_SO_SALES_DATE_ROUTE,
				CONCAT(MATERIAL_NONE_SALES_DURATION, '->',
				T.COMPONENT_NONE_SALES_DURATION)                               AS NONE_SALES_DURATION_ROUTE,
				CONCAT(MATERIAL_NONE_SALES_PERIOD, '->',
				T.COMPONENT_NONE_SALES_PERIOD)                                 AS NONE_SALES_PERIOD_ROUTE,
				CONCAT(MATERIAL_GRA, '->', T.COMPONENT_GRA)                           AS GRA_ROUTE,
				CONCAT(MATERIAL_COMMODITY_CODE, '->', T.COMPONENT_COMMODITY_CODE)     AS COMMODITY_CODE_ROUTE,
				CONCAT(MATERIAL_STARS_DESCRIPTION, '->',
				T.COMPONENT_STARS_DESCRIPTION)                                 AS STARS_DESCRIPTION_ROUTE,
				CONCAT(MATERIAL_PLANNING_ALIVE_STATUS, '->',
				T.COMPONENT_PLANNING_ALIVE_STATUS)                             AS PLANNING_ALIVE_STATUS_ROUTE,
				CONCAT(MATERIAL_XBOARD, '->', T.COMPONENT_XBOARD)                     AS XBOARD_ROUTE,
				CONCAT(MATERIAL_RES_COV_STK, '->', T.COMPONENT_RES_COV_STK)           AS RES_COV_STK_ROUTE,
				CONCAT(MATERIAL_RES_COV_STK_RANGE, '->',
				T.COMPONENT_RES_COV_STK_RANGE)                                 AS RES_COV_STK_RANGE_ROUTE,
				CONCAT(MATERIAL_RES_COV_STK_LA, '->', T.COMPONENT_RES_COV_STK_LA)     AS RES_COV_STK_LA_ROUTE,
				CONCAT(MATERIAL_RES_COV_STK_LA_RANGE, '->',
				T.COMPONENT_RES_COV_STK_LA_RANGE)                              AS RES_COV_STK_LA_RANGE_ROUTE,
				CONCAT(MATERIAL_RES_COV_STK_OPO, '->', T.COMPONENT_RES_COV_STK_OPO)   AS RES_COV_STK_OPO_ROUTE,
				CONCAT(MATERIAL_RES_COV_STK_OPO_RANGE, '->',
				T.COMPONENT_RES_COV_STK_OPO_RANGE)                             AS RES_COV_STK_OPO_RANGE_ROUTE,
				CONCAT(MATERIAL_FIRST_CONSUMPTION_RANGE, '->',
				T.COMPONENT_FIRST_CONSUMPTION_RANGE)                           AS FIRST_CONSUMPTION_RANGE_ROUTE,
				CONCAT(MATERIAL_FIRST_PO_CREATE_RANGE, '->',
				T.COMPONENT_FIRST_PO_CREATE_RANGE)                             AS FIRST_PO_CREATE_RANGE_ROUTE,
				CONCAT(MATERIAL_FIRST_SO_CREATE_RANGE, '->',
				T.COMPONENT_FIRST_SO_CREATE_RANGE)                             AS FIRST_SO_CREATE_RANGE_ROUTE,
				CONCAT(MATERIAL_LAST_SO_SALES_RANGE, '->',
				T.COMPONENT_LAST_SO_SALES_RANGE)                               AS LAST_SO_SALES_RANGE_ROUTE,
				CONCAT(MATERIAL_PRD_BOM_COMPONENT, '->',
				T.COMPONENT_PRD_BOM_COMPONENT)                                 AS PRD_BOM_COMPONENT_ROUTE,
				CONCAT(MATERIAL_FIRST_CONS_DATE, '->', T.COMPONENT_FIRST_CONS_DATE)   AS FIRST_CONS_DATE_ROUTE,
				CONCAT(MATERIAL_FIRST_SO_DATE, '->', T.COMPONENT_FIRST_SO_DATE)       AS FIRST_SO_DATE_ROUTE,
				CONCAT(MATERIAL_FIRST_PO_DATE, '->', T.COMPONENT_FIRST_PO_DATE)       AS FIRST_PO_DATE_ROUTE,
				CONCAT(MATERIAL_COV_RANGE, '->', T.COMPONENT_COV_RANGE)               AS COV_RANGE_ROUTE,
				CONCAT(MATERIAL_RISK_LEVEL, '->', T.COMPONENT_RISK_LEVEL)             AS RISK_LEVEL_ROUTE,
				CONCAT(MATERIAL_ADU_END_ORDER, '->', T.COMPONENT_ADU_END_ORDER)       AS ADU_END_ORDER_ROUTE,
				CONCAT(MATERIAL_STDDEV_END_ORDER, '->', T.COMPONENT_STDDEV_END_ORDER) AS STDDEV_END_ORDER_ROUTE,
				CONCAT(MATERIAL_COV_END_ORDER, '->', T.COMPONENT_COV_END_ORDER)       AS COV_END_ORDER_ROUTE,
				CONCAT(MATERIAL_COV_RANGE_END_ORDER, '->',
				T.COMPONENT_COV_RANGE_END_ORDER)                               AS COV_RANGE_END_ORDER_ROUTE,
				CONCAT(MATERIAL_NUM_OF_WHERE_USE, '->', T.COMPONENT_NUM_OF_WHERE_USE) AS NUM_OF_WHERE_USE_ROUTE,
				CONCAT(MATERIAL_NUM_OF_WHERE_USE_RANGE, '->',
				T.COMPONENT_NUM_OF_WHERE_USE_RANGE)                            AS NUM_OF_WHERE_USE_RANGE_ROUTE,
				CONCAT(MATERIAL_PRODUCT_GROUP_A, '->', T.COMPONENT_PRODUCT_GROUP_A)   AS PRODUCT_GROUP_A_ROUTE,
				CONCAT(MATERIAL_PRODUCT_GROUP_B, '->', T.COMPONENT_PRODUCT_GROUP_B)   AS PRODUCT_GROUP_B_ROUTE,
				CONCAT(MATERIAL_PRODUCT_GROUP_C, '->', T.COMPONENT_PRODUCT_GROUP_C)   AS PRODUCT_GROUP_C_ROUTE,
				CONCAT(MATERIAL_PRODUCT_GROUP_D, '->', T.COMPONENT_PRODUCT_GROUP_D)   AS PRODUCT_GROUP_D_ROUTE,
				CONCAT(MATERIAL_PRODUCT_GROUP_E, '->', T.COMPONENT_PRODUCT_GROUP_E)   AS PRODUCT_GROUP_E_ROUTE,
				CONCAT(MATERIAL_TOTAL_STOCK_QTY, '->', T.COMPONENT_TOTAL_STOCK_QTY)   AS TOTAL_STOCK_QTY_ROUTE,
				CONCAT(MATERIAL_UU_STOCK, '->', T.COMPONENT_UU_STOCK)                 AS UU_STOCK_ROUTE,
				CONCAT(MATERIAL_STOCK_IN_QI, '->', T.COMPONENT_STOCK_IN_QI)           AS QI_STOCK_QTY_ROUTE,
				CONCAT(MATERIAL_BLOCKED_STOCK, '->', T.COMPONENT_BLOCKED_STOCK)       AS BLOCKED_STOCK_ROUTE,
				CONCAT(MATERIAL_PAST_DUE_SO_QTY, '->', T.COMPONENT_PAST_DUE_SO_QTY)   AS PAST_DUE_SO_QTY_ROUTE,
				CONCAT(MATERIAL_PAST_DUE_SO_QTY_NON_BLOCK, '->',
				T.COMPONENT_PAST_DUE_SO_QTY_NON_BLOCK)                         AS PAST_DUE_SO_QTY_NON_BLOCK_ROUTE,
				CONCAT(MATERIAL_TOTAL_OPEN_SO_QTY, '->',
				T.COMPONENT_TOTAL_OPEN_SO_QTY)                                 AS TOTAL_OPEN_SO_QTY_ROUTE,
				CONCAT(MATERIAL_WITHIN_LT_SO_QTY, '->', T.COMPONENT_WITHIN_LT_SO_QTY) AS WITHIN_LT_SO_QTY_ROUTE,
				CONCAT(MATERIAL_WITHOUT_LT_SO_QTY, '->',
				T.COMPONENT_WITHOUT_LT_SO_QTY)                                 AS WITHOUT_LT_SO_QTY_ROUTE,
				CONCAT(MATERIAL_TOTAL_OPEN_SO_QTY_NON_BLOCK, '->',
				T.COMPONENT_TOTAL_OPEN_SO_QTY_NON_BLOCK)                       AS TOTAL_OPEN_SO_QTY_NON_BLOCK_ROUTE,
				CONCAT(MATERIAL_WITHIN_LT_SO_NON_BLOCK, '->',
				T.COMPONENT_WITHIN_LT_SO_NON_BLOCK)                            AS WITHIN_LT_SO_NON_BLOCK_ROUTE,
				CONCAT(MATERIAL_WITHOUT_LT_SO_NON_BLOCK, '->',
				T.COMPONENT_WITHOUT_LT_SO_NON_BLOCK)                           AS WITHOUT_LT_SO_NON_BLOCK_ROUTE,
				CONCAT(MATERIAL_OPEN_PO_QTY, '->', T.COMPONENT_OPEN_PO_QTY)           AS OPEN_PO_QTY_ROUTE,
				CONCAT(MATERIAL_OPEN_SO_QTY, '->', T.COMPONENT_OPEN_SO_QTY)           AS OPEN_SO_QTY_ROUTE,
				CONCAT(MATERIAL_ORDER_RESERVATION_QTY, '->',
				T.COMPONENT_ORDER_RESERVATION_QTY)                             AS ORDER_RESERVATION_QTY_ROUTE,
				CONCAT(MATERIAL_PASTDUE_PO_QTY, '->', T.COMPONENT_PASTDUE_PO_QTY)     AS PASTDUE_PO_QTY_ROUTE,
				CONCAT(MATERIAL_PO_AB, '->', T.COMPONENT_PO_AB)                       AS OPEN_PO_AB_ROUTE,
				CONCAT(MATERIAL_PO_LA, '->', T.COMPONENT_PO_LA)                       AS OPEN_PO_LA_ROUTE,
				CONCAT(MATERIAL_PASTDUE_ORDER_RESERVATION, '->',
				T.COMPONENT_PASTDUE_ORDER_RESERVATION)                         AS PASTDUE_ORDER_RESERVATION_ROUTE,
				CONCAT(MATERIAL_ORDER_RESERVATION_QTY_WITHIN_7_DAYS, '->',
				COMPONENT_ORDER_RESERVATION_QTY_WITHIN_7_DAYS)                 AS ORDER_RESERVATION_QTY_WITHIN_7_DAYS_ROUTE,
				CONCAT(
				MATERIAL_ORDER_RESERVATION_QTY_WITHOUT_7_DAYS, '->',
				COMPONENT_ORDER_RESERVATION_QTY_WITHOUT_7_DAYS)               AS ORDER_RESERVATION_QTY_WITHOUT_7_DAYS_ROUTE,
				[toString(BOM_COMPONENT)]       AS path
				FROM scpc.`CRITICAL_BOM_V` T
				INNER JOIN (SELECT MATERIAL, PLANT_CODE
				FROM scpc.CRITICAL_BOM_MANUAL
				WHERE USER_ID = #{session.userid, jdbcType=VARCHAR}) M
				ON M.MATERIAL = T.BOM_COMPONENT AND M.PLANT_CODE = T.PLANT_CODE
				WHERE PLANT_CODE IN
				<foreach collection="plant" item="plantItem" open="(" separator="," close=")">
					#{plantItem, jdbcType=VARCHAR}
				</foreach>

				UNION ALL

				SELECT p.ROOT_MATERIAL,
				c.MATERIAL                      AS PARENT_MATERIAL,
				c.BOM_COMPONENT                 AS CHILD_MATERIAL,
				concat( c.MATERIAL, '->',ROUTE) AS ROUTE,
				concat(c.PLANT_CODE, '->', PLANT_ROUTE),
				concat(c.COMPONENT_QTY, '->', COMPONENT_QTY_ROUTE),
				c.MATERIAL,
				c.PLANT_CODE,
				p.BOTTOM,
				p.BOTTOM_PLANT_CODE,
				CASE
				WHEN p.TRANSFER_NUMBER ='' AND c.PO_MATERIAL !=''
				THEN  c.PO_MATERIAL || '&lt;->' || c.BOM_COMPONENT
				WHEN p.TRANSFER_NUMBER !='' AND c.PO_MATERIAL !=''
				THEN '/' || c.PO_MATERIAL || '&lt;->' || c.BOM_COMPONENT
				ELSE p.TRANSFER_NUMBER END  AS TRANSFER_NUMBER,
				CONCAT(COMPONENT_PLANT_TYPE, '->', PLANT_TYPE_ROUTE),
				CONCAT(COMPONENT_CLUSTER_NAME, '->', CLUSTER_NAME_ROUTE),
				CONCAT(COMPONENT_ENTITY, '->', ENTITY_ROUTE),
				CONCAT(COMPONENT_PRODUCT_LINE, '->', PRODUCT_LINE_ROUTE),
				CONCAT(COMPONENT_PRODUCTION_LINE, '->', PRODUCTION_LINE_ROUTE),
				CONCAT(COMPONENT_LOCAL_PRODUCT_LINE, '->', LOCAL_PRODUCT_LINE_ROUTE),
				CONCAT(COMPONENT_LOCAL_PRODUCT_FAMILY, '->', LOCAL_PRODUCT_FAMILY_ROUTE),
				CONCAT(COMPONENT_LOCAL_PRODUCT_SUBFAMILY, '->', LOCAL_PRODUCT_SUBFAMILY_ROUTE),
				CONCAT(COMPONENT_HYPER_CARE_LEVEL, '->', HYPER_CARE_LEVEL_ROUTE),
				CONCAT(COMPONENT_MRP_CONTROLLER, '->', MRP_CONTROLLER_ROUTE),
				CONCAT(COMPONENT_STOCKING_POLICY, '->', STOCKING_POLICY_ROUTE),
				CONCAT(COMPONENT_VENDOR_CODE, '->', VENDOR_CODE_ROUTE),
				CONCAT(COMPONENT_VENDOR_NAME, '->', VENDOR_NAME_ROUTE),
				CONCAT(COMPONENT_VENDOR_SHORT_NAME, '->', VENDOR_SHORT_NAME_ROUTE),
				CONCAT(COMPONENT_VENDOR_FULL_NAME, '->', VENDOR_FULL_NAME_ROUTE),
				CONCAT(COMPONENT_VENDOR_PARENT_NAME, '->', VENDOR_PARENT_NAME_ROUTE),
				CONCAT(COMPONENT_VENDOR_PARENT_CODE, '->', VENDOR_PARENT_CODE_ROUTE),
				CONCAT(COMPONENT_VENDOR_MATERIAL, '->', VENDOR_MATERIAL_ROUTE),
				CONCAT(COMPONENT_SOURCE_CATEGORY, '->', SOURCE_CATEGORY_ROUTE),
				CONCAT(COMPONENT_PLANNED_DELIV_TIME, '->', PLANNED_DELIV_TIME_ROUTE),
				CONCAT(COMPONENT_LT_RANGE, '->', LT_RANGE_ROUTE),
				CONCAT(COMPONENT_UNIT_COST, '->', UNIT_COST_ROUTE),
				CONCAT(COMPONENT_SAFETY_STOCK, '->', SAFETY_STOCK_ROUTE),
				CONCAT(COMPONENT_AMU_ONE_MM, '->', AMU_ONE_MM_ROUTE),
				CONCAT(COMPONENT_AVG_MONTHLY_ORDER_INTAKE, '->', AVG_MONTHLY_ORDER_INTAKE_ROUTE),
				CONCAT(COMPONENT_AMF_ONE_MM, '->', AMF_ONE_MM_ROUTE),
				CONCAT(COMPONENT_COV, '->', COV_ROUTE),
				CONCAT(COMPONENT_SS2, '->', SS2_ROUTE),
				CONCAT(COMPONENT_SS3, '->', SS3_ROUTE),
				CONCAT(COMPONENT_CALCULATED_ABC, '->', CALCULATED_ABC_ROUTE),
				CONCAT(COMPONENT_CALCULATED_FMR, '->', CALCULATED_FMR_ROUTE),
				CONCAT(COMPONENT_NEW_PRODUCTS, '->', NEW_PRODUCTS_ROUTE),
				CONCAT(COMPONENT_PROCUREMENT_TYPE, '->', PROCUREMENT_TYPE_ROUTE),
				CONCAT(COMPONENT_FOLLOW_UP_MATERIAL, '->', FOLLOW_UP_MATERIAL_ROUTE),
				CONCAT(COMPONENT_PRODN_SUPERVISOR, '->', PRODN_SUPERVISOR_ROUTE),
				CONCAT(COMPONENT_IN_HOUSE_PRODN_LT, '->', IN_HOUSE_PRODN_LT_ROUTE),
				CONCAT(COMPONENT_STARS_CODE, '->', STARS_CODE_ROUTE),
				CONCAT(COMPONENT_LAST_SO_SALES_DATE, '->', LAST_SO_SALES_DATE_ROUTE),
				CONCAT(COMPONENT_NONE_SALES_DURATION, '->', NONE_SALES_DURATION_ROUTE),
				CONCAT(COMPONENT_NONE_SALES_PERIOD, '->', NONE_SALES_PERIOD_ROUTE),
				CONCAT(COMPONENT_GRA, '->', GRA_ROUTE),
				CONCAT(COMPONENT_COMMODITY_CODE, '->', COMMODITY_CODE_ROUTE),
				CONCAT(COMPONENT_STARS_DESCRIPTION, '->', STARS_DESCRIPTION_ROUTE),
				CONCAT(COMPONENT_PLANNING_ALIVE_STATUS, '->', PLANNING_ALIVE_STATUS_ROUTE),
				CONCAT(COMPONENT_XBOARD, '->', XBOARD_ROUTE),
				CONCAT(COMPONENT_RES_COV_STK, '->', RES_COV_STK_ROUTE),
				CONCAT(COMPONENT_RES_COV_STK_RANGE, '->', RES_COV_STK_RANGE_ROUTE),
				CONCAT(COMPONENT_RES_COV_STK_LA, '->', RES_COV_STK_LA_ROUTE),
				CONCAT(COMPONENT_RES_COV_STK_LA_RANGE, '->', RES_COV_STK_LA_RANGE_ROUTE),
				CONCAT(COMPONENT_RES_COV_STK_OPO, '->', RES_COV_STK_OPO_ROUTE),
				CONCAT(COMPONENT_RES_COV_STK_OPO_RANGE, '->', RES_COV_STK_OPO_RANGE_ROUTE),
				CONCAT(COMPONENT_FIRST_CONSUMPTION_RANGE, '->', FIRST_CONSUMPTION_RANGE_ROUTE),
				CONCAT(COMPONENT_FIRST_PO_CREATE_RANGE, '->', FIRST_PO_CREATE_RANGE_ROUTE),
				CONCAT(FIRST_SO_CREATE_RANGE_ROUTE, '->', COMPONENT_FIRST_SO_CREATE_RANGE),
				CONCAT(LAST_SO_SALES_RANGE_ROUTE, '->', COMPONENT_LAST_SO_SALES_RANGE),
				CONCAT(PRD_BOM_COMPONENT_ROUTE, '->', COMPONENT_PRD_BOM_COMPONENT),
				CONCAT(FIRST_CONS_DATE_ROUTE, '->', COMPONENT_FIRST_CONS_DATE),
				CONCAT(FIRST_SO_DATE_ROUTE, '->', COMPONENT_FIRST_SO_DATE),
				CONCAT(FIRST_PO_DATE_ROUTE, '->', COMPONENT_FIRST_PO_DATE),
				CONCAT(COV_RANGE_ROUTE, '->', COMPONENT_COV_RANGE),
				CONCAT(RISK_LEVEL_ROUTE, '->', COMPONENT_RISK_LEVEL),
				CONCAT(ADU_END_ORDER_ROUTE, '->', COMPONENT_ADU_END_ORDER),
				CONCAT(STDDEV_END_ORDER_ROUTE, '->', COMPONENT_STDDEV_END_ORDER),
				CONCAT(COV_END_ORDER_ROUTE, '->', COMPONENT_COV_END_ORDER),
				CONCAT(COV_RANGE_END_ORDER_ROUTE, '->', COMPONENT_COV_RANGE_END_ORDER),
				CONCAT(NUM_OF_WHERE_USE_ROUTE, '->', COMPONENT_NUM_OF_WHERE_USE),
				CONCAT(NUM_OF_WHERE_USE_RANGE_ROUTE, '->', COMPONENT_NUM_OF_WHERE_USE_RANGE),
				CONCAT(COMPONENT_PRODUCT_GROUP_A, '->', PRODUCT_GROUP_A_ROUTE),
				CONCAT(COMPONENT_PRODUCT_GROUP_B, '->', PRODUCT_GROUP_B_ROUTE),
				CONCAT(COMPONENT_PRODUCT_GROUP_C, '->', PRODUCT_GROUP_C_ROUTE),
				CONCAT(COMPONENT_PRODUCT_GROUP_D, '->', PRODUCT_GROUP_D_ROUTE),
				CONCAT(COMPONENT_PRODUCT_GROUP_E, '->', PRODUCT_GROUP_E_ROUTE),
				CONCAT(COMPONENT_TOTAL_STOCK_QTY, '->', TOTAL_STOCK_QTY_ROUTE),
				CONCAT(COMPONENT_UU_STOCK, '->', UU_STOCK_ROUTE),
				CONCAT(COMPONENT_STOCK_IN_QI, '->', QI_STOCK_QTY_ROUTE),
				CONCAT(COMPONENT_BLOCKED_STOCK, '->', BLOCKED_STOCK_ROUTE),
				CONCAT(COMPONENT_PAST_DUE_SO_QTY, '->', PAST_DUE_SO_QTY_ROUTE),
				CONCAT(COMPONENT_PAST_DUE_SO_QTY_NON_BLOCK , '->', PAST_DUE_SO_QTY_NON_BLOCK_ROUTE),
				CONCAT(COMPONENT_TOTAL_OPEN_SO_QTY, '->', TOTAL_OPEN_SO_QTY_ROUTE),
				CONCAT(COMPONENT_WITHIN_LT_SO_QTY, '->', WITHIN_LT_SO_QTY_ROUTE),
				CONCAT(COMPONENT_WITHOUT_LT_SO_QTY, '->', WITHOUT_LT_SO_QTY_ROUTE),
				CONCAT(COMPONENT_TOTAL_OPEN_SO_QTY_NON_BLOCK, '->', TOTAL_OPEN_SO_QTY_NON_BLOCK_ROUTE),
				CONCAT(COMPONENT_WITHIN_LT_SO_NON_BLOCK, '->', WITHIN_LT_SO_NON_BLOCK_ROUTE),
				CONCAT(COMPONENT_WITHOUT_LT_SO_NON_BLOCK, '->', WITHOUT_LT_SO_NON_BLOCK_ROUTE),
				CONCAT(COMPONENT_OPEN_PO_QTY, '->', OPEN_PO_QTY_ROUTE),
				CONCAT(COMPONENT_OPEN_SO_QTY, '->', OPEN_SO_QTY_ROUTE),
				CONCAT(COMPONENT_ORDER_RESERVATION_QTY, '->', ORDER_RESERVATION_QTY_ROUTE),
				CONCAT(COMPONENT_PASTDUE_PO_QTY, '->', PASTDUE_PO_QTY_ROUTE),
				CONCAT(COMPONENT_PO_AB, '->', OPEN_PO_AB_ROUTE),
				CONCAT(COMPONENT_PO_LA, '->', OPEN_PO_LA_ROUTE),
				CONCAT(COMPONENT_PASTDUE_ORDER_RESERVATION, '->', PASTDUE_ORDER_RESERVATION_ROUTE),
				CONCAT(COMPONENT_ORDER_RESERVATION_QTY_WITHIN_7_DAYS, '->',
				ORDER_RESERVATION_QTY_WITHIN_7_DAYS_ROUTE),
				CONCAT(COMPONENT_ORDER_RESERVATION_QTY_WITHOUT_7_DAYS, '->',
				ORDER_RESERVATION_QTY_WITHOUT_7_DAYS_ROUTE),
				arrayConcat(p.path, [toString(c.MATERIAL)])
				FROM scpc.CRITICAL_BOM_V AS c
				JOIN bom_tree AS p
				ON c.BOM_COMPONENT = p.PARENT_MATERIAL
				WHERE NOT has(arraySlice(p.path, 1, length(p.path)-1), toString(c.MATERIAL))
				AND PLANT_CODE IN
				<foreach collection="plant" item="plantItem" open="(" separator="," close=")">
					#{plantItem, jdbcType=VARCHAR}
				</foreach>)
				SELECT DISTINCT *
				FROM bom_tree

			</when>
			<otherwise>
				WITH RECURSIVE bom_tree AS (
				SELECT BOM_COMPONENT                                                         AS ROOT_MATERIAL,
				MATERIAL                                                              AS PARENT_MATERIAL,
				BOM_COMPONENT                                                         AS CHILD_MATERIAL,
				CONCAT(MATERIAL, '->', BOM_COMPONENT)                                 AS ROUTE,
				CONCAT(PLANT_CODE, '->', PLANT_CODE)                                  AS PLANT_ROUTE,
				CONCAT('1', '->', COMPONENT_QTY)                                      AS COMPONENT_QTY_ROUTE,
				MATERIAL                                                              AS TOP,
				PLANT_CODE                                                            AS TOP_PLANT_CODE,
				BOM_COMPONENT                                                         AS BOTTOM,
				PLANT_CODE                                                            AS BOTTOM_PLANT_CODE,
				CASE
				WHEN PO_MATERIAL !=''
				THEN CONCAT(BOM_COMPONENT, '&lt;->', PO_MATERIAL)
				ELSE '' END                                                       AS TRANSFER_NUMBER,
				CONCAT(MATERIAL_PLANT_TYPE, '->', COMPONENT_PLANT_TYPE)               AS PLANT_TYPE_ROUTE,
				CONCAT(MATERIAL_CLUSTER_NAME, '->', COMPONENT_CLUSTER_NAME)           AS CLUSTER_NAME_ROUTE,
				CONCAT(MATERIAL_ENTITY, '->', COMPONENT_ENTITY)                       AS ENTITY_ROUTE,
				CONCAT(MATERIAL_PRODUCT_LINE, '->', COMPONENT_PRODUCT_LINE)           AS PRODUCT_LINE_ROUTE,
				CONCAT(MATERIAL_PRODUCTION_LINE, '->', T.COMPONENT_PRODUCTION_LINE)   AS PRODUCTION_LINE_ROUTE,
				CONCAT(MATERIAL_LOCAL_PRODUCT_LINE, '->',
				T.COMPONENT_LOCAL_PRODUCT_LINE)                                AS LOCAL_PRODUCT_LINE_ROUTE,
				CONCAT(MATERIAL_LOCAL_PRODUCT_FAMILY, '->',
				T.COMPONENT_LOCAL_PRODUCT_FAMILY)                              AS LOCAL_PRODUCT_FAMILY_ROUTE,
				CONCAT(MATERIAL_LOCAL_PRODUCT_SUBFAMILY, '->',
				T.COMPONENT_LOCAL_PRODUCT_SUBFAMILY)                           AS LOCAL_PRODUCT_SUBFAMILY_ROUTE,
				CONCAT(MATERIAL_HYPER_CARE_LEVEL, '->', T.COMPONENT_HYPER_CARE_LEVEL) AS HYPER_CARE_LEVEL_ROUTE,
				CONCAT(MATERIAL_MRP_CONTROLLER, '->', T.COMPONENT_MRP_CONTROLLER)     AS MRP_CONTROLLER_ROUTE,
				CONCAT(MATERIAL_STOCKING_POLICY, '->', T.COMPONENT_STOCKING_POLICY)   AS STOCKING_POLICY_ROUTE,
				CONCAT(MATERIAL_VENDOR_CODE, '->', T.COMPONENT_VENDOR_CODE)           AS VENDOR_CODE_ROUTE,
				CONCAT(MATERIAL_VENDOR_NAME, '->', T.COMPONENT_VENDOR_NAME)           AS VENDOR_NAME_ROUTE,
				CONCAT(MATERIAL_VENDOR_SHORT_NAME, '->',
				T.COMPONENT_VENDOR_SHORT_NAME)                                 AS VENDOR_SHORT_NAME_ROUTE,
				CONCAT(MATERIAL_VENDOR_FULL_NAME, '->', T.COMPONENT_VENDOR_FULL_NAME) AS VENDOR_FULL_NAME_ROUTE,
				CONCAT(MATERIAL_VENDOR_PARENT_NAME, '->',
				T.COMPONENT_VENDOR_PARENT_NAME)                                AS VENDOR_PARENT_NAME_ROUTE,
				CONCAT(MATERIAL_VENDOR_PARENT_CODE, '->',
				T.COMPONENT_VENDOR_PARENT_CODE)                                AS VENDOR_PARENT_CODE_ROUTE,
				CONCAT(MATERIAL_VENDOR_MATERIAL, '->', T.COMPONENT_VENDOR_MATERIAL)   AS VENDOR_MATERIAL_ROUTE,
				CONCAT(MATERIAL_SOURCE_CATEGORY, '->', T.COMPONENT_SOURCE_CATEGORY)   AS SOURCE_CATEGORY_ROUTE,
				CONCAT(MATERIAL_PLANNED_DELIV_TIME, '->',
				T.COMPONENT_PLANNED_DELIV_TIME)                                AS PLANNED_DELIV_TIME_ROUTE,
				CONCAT(MATERIAL_LT_RANGE, '->', T.COMPONENT_LT_RANGE)                 AS LT_RANGE_ROUTE,
				CONCAT(MATERIAL_UNIT_COST, '->', T.COMPONENT_UNIT_COST)               AS UNIT_COST_ROUTE,
				CONCAT(MATERIAL_SAFETY_STOCK, '->', T.COMPONENT_SAFETY_STOCK)         AS SAFETY_STOCK_ROUTE,
				CONCAT(MATERIAL_AMU_ONE_MM, '->', T.COMPONENT_AMU_ONE_MM)             AS AMU_ONE_MM_ROUTE,
				CONCAT(MATERIAL_AVG_MONTHLY_ORDER_INTAKE, '->',
				T.COMPONENT_AVG_MONTHLY_ORDER_INTAKE)                          AS AVG_MONTHLY_ORDER_INTAKE_ROUTE,
				CONCAT(MATERIAL_AMF_ONE_MM, '->', T.COMPONENT_AMF_ONE_MM)             AS AMF_ONE_MM_ROUTE,
				CONCAT(MATERIAL_COV, '->', T.COMPONENT_COV)                           AS COV_ROUTE,
				CONCAT(MATERIAL_SS2, '->', T.COMPONENT_SS2)                           AS SS2_ROUTE,
				CONCAT(MATERIAL_SS3, '->', T.COMPONENT_SS3)                           AS SS3_ROUTE,
				CONCAT(MATERIAL_CALCULATED_ABC, '->', T.COMPONENT_CALCULATED_ABC)     AS CALCULATED_ABC_ROUTE,
				CONCAT(MATERIAL_CALCULATED_FMR, '->', T.COMPONENT_CALCULATED_FMR)     AS CALCULATED_FMR_ROUTE,
				CONCAT(MATERIAL_NEW_PRODUCTS, '->', T.COMPONENT_NEW_PRODUCTS)         AS NEW_PRODUCTS_ROUTE,
				CONCAT(MATERIAL_PROCUREMENT_TYPE, '->', T.COMPONENT_PROCUREMENT_TYPE) AS PROCUREMENT_TYPE_ROUTE,
				CONCAT(MATERIAL_FOLLOW_UP_MATERIAL, '->',
				T.COMPONENT_FOLLOW_UP_MATERIAL)                                AS FOLLOW_UP_MATERIAL_ROUTE,
				CONCAT(MATERIAL_PRODN_SUPERVISOR, '->', T.COMPONENT_PRODN_SUPERVISOR) AS PRODN_SUPERVISOR_ROUTE,
				CONCAT(MATERIAL_IN_HOUSE_PRODN_LT, '->',
				T.COMPONENT_IN_HOUSE_PRODN_LT)                                 AS IN_HOUSE_PRODN_LT_ROUTE,
				CONCAT(MATERIAL_STARS_CODE, '->', T.COMPONENT_STARS_CODE)             AS STARS_CODE_ROUTE,
				CONCAT(MATERIAL_LAST_SO_SALES_DATE, '->',
				T.COMPONENT_LAST_SO_SALES_DATE)                                AS LAST_SO_SALES_DATE_ROUTE,
				CONCAT(MATERIAL_NONE_SALES_DURATION, '->',
				T.COMPONENT_NONE_SALES_DURATION)                               AS NONE_SALES_DURATION_ROUTE,
				CONCAT(MATERIAL_NONE_SALES_PERIOD, '->',
				T.COMPONENT_NONE_SALES_PERIOD)                                 AS NONE_SALES_PERIOD_ROUTE,
				CONCAT(MATERIAL_GRA, '->', T.COMPONENT_GRA)                           AS GRA_ROUTE,
				CONCAT(MATERIAL_COMMODITY_CODE, '->', T.COMPONENT_COMMODITY_CODE)     AS COMMODITY_CODE_ROUTE,
				CONCAT(MATERIAL_STARS_DESCRIPTION, '->',
				T.COMPONENT_STARS_DESCRIPTION)                                 AS STARS_DESCRIPTION_ROUTE,
				CONCAT(MATERIAL_PLANNING_ALIVE_STATUS, '->',
				T.COMPONENT_PLANNING_ALIVE_STATUS)                             AS PLANNING_ALIVE_STATUS_ROUTE,
				CONCAT(MATERIAL_XBOARD, '->', T.COMPONENT_XBOARD)                     AS XBOARD_ROUTE,
				CONCAT(MATERIAL_RES_COV_STK, '->', T.COMPONENT_RES_COV_STK)           AS RES_COV_STK_ROUTE,
				CONCAT(MATERIAL_RES_COV_STK_RANGE, '->',
				T.COMPONENT_RES_COV_STK_RANGE)                                 AS RES_COV_STK_RANGE_ROUTE,
				CONCAT(MATERIAL_RES_COV_STK_LA, '->', T.COMPONENT_RES_COV_STK_LA)     AS RES_COV_STK_LA_ROUTE,
				CONCAT(MATERIAL_RES_COV_STK_LA_RANGE, '->',
				T.COMPONENT_RES_COV_STK_LA_RANGE)                              AS RES_COV_STK_LA_RANGE_ROUTE,
				CONCAT(MATERIAL_RES_COV_STK_OPO, '->', T.COMPONENT_RES_COV_STK_OPO)   AS RES_COV_STK_OPO_ROUTE,
				CONCAT(MATERIAL_RES_COV_STK_OPO_RANGE, '->',
				T.COMPONENT_RES_COV_STK_OPO_RANGE)                             AS RES_COV_STK_OPO_RANGE_ROUTE,
				CONCAT(MATERIAL_FIRST_CONSUMPTION_RANGE, '->',
				T.COMPONENT_FIRST_CONSUMPTION_RANGE)                           AS FIRST_CONSUMPTION_RANGE_ROUTE,
				CONCAT(MATERIAL_FIRST_PO_CREATE_RANGE, '->',
				T.COMPONENT_FIRST_PO_CREATE_RANGE)                             AS FIRST_PO_CREATE_RANGE_ROUTE,
				CONCAT(MATERIAL_FIRST_SO_CREATE_RANGE, '->',
				T.COMPONENT_FIRST_SO_CREATE_RANGE)                             AS FIRST_SO_CREATE_RANGE_ROUTE,
				CONCAT(MATERIAL_LAST_SO_SALES_RANGE, '->',
				T.COMPONENT_LAST_SO_SALES_RANGE)                               AS LAST_SO_SALES_RANGE_ROUTE,
				CONCAT(MATERIAL_PRD_BOM_COMPONENT, '->',
				T.COMPONENT_PRD_BOM_COMPONENT)                                 AS PRD_BOM_COMPONENT_ROUTE,
				CONCAT(MATERIAL_FIRST_CONS_DATE, '->', T.COMPONENT_FIRST_CONS_DATE)   AS FIRST_CONS_DATE_ROUTE,
				CONCAT(MATERIAL_FIRST_SO_DATE, '->', T.COMPONENT_FIRST_SO_DATE)       AS FIRST_SO_DATE_ROUTE,
				CONCAT(MATERIAL_FIRST_PO_DATE, '->', T.COMPONENT_FIRST_PO_DATE)       AS FIRST_PO_DATE_ROUTE,
				CONCAT(MATERIAL_COV_RANGE, '->', T.COMPONENT_COV_RANGE)               AS COV_RANGE_ROUTE,
				CONCAT(MATERIAL_RISK_LEVEL, '->', T.COMPONENT_RISK_LEVEL)             AS RISK_LEVEL_ROUTE,
				CONCAT(MATERIAL_ADU_END_ORDER, '->', T.COMPONENT_ADU_END_ORDER)       AS ADU_END_ORDER_ROUTE,
				CONCAT(MATERIAL_STDDEV_END_ORDER, '->', T.COMPONENT_STDDEV_END_ORDER) AS STDDEV_END_ORDER_ROUTE,
				CONCAT(MATERIAL_COV_END_ORDER, '->', T.COMPONENT_COV_END_ORDER)       AS COV_END_ORDER_ROUTE,
				CONCAT(MATERIAL_COV_RANGE_END_ORDER, '->',
				T.COMPONENT_COV_RANGE_END_ORDER)                               AS COV_RANGE_END_ORDER_ROUTE,
				CONCAT(MATERIAL_NUM_OF_WHERE_USE, '->', T.COMPONENT_NUM_OF_WHERE_USE) AS NUM_OF_WHERE_USE_ROUTE,
				CONCAT(MATERIAL_NUM_OF_WHERE_USE_RANGE, '->',
				T.COMPONENT_NUM_OF_WHERE_USE_RANGE)                            AS NUM_OF_WHERE_USE_RANGE_ROUTE,
				CONCAT(MATERIAL_PRODUCT_GROUP_A, '->', T.COMPONENT_PRODUCT_GROUP_A)   AS PRODUCT_GROUP_A_ROUTE,
				CONCAT(MATERIAL_PRODUCT_GROUP_B, '->', T.COMPONENT_PRODUCT_GROUP_B)   AS PRODUCT_GROUP_B_ROUTE,
				CONCAT(MATERIAL_PRODUCT_GROUP_C, '->', T.COMPONENT_PRODUCT_GROUP_C)   AS PRODUCT_GROUP_C_ROUTE,
				CONCAT(MATERIAL_PRODUCT_GROUP_D, '->', T.COMPONENT_PRODUCT_GROUP_D)   AS PRODUCT_GROUP_D_ROUTE,
				CONCAT(MATERIAL_PRODUCT_GROUP_E, '->', T.COMPONENT_PRODUCT_GROUP_E)   AS PRODUCT_GROUP_E_ROUTE,
				CONCAT(MATERIAL_TOTAL_STOCK_QTY, '->', T.COMPONENT_TOTAL_STOCK_QTY)   AS TOTAL_STOCK_QTY_ROUTE,
				CONCAT(MATERIAL_UU_STOCK, '->', T.COMPONENT_UU_STOCK)                 AS UU_STOCK_ROUTE,
				CONCAT(MATERIAL_STOCK_IN_QI, '->', T.COMPONENT_STOCK_IN_QI)           AS QI_STOCK_QTY_ROUTE,
				CONCAT(MATERIAL_BLOCKED_STOCK, '->', T.COMPONENT_BLOCKED_STOCK)       AS BLOCKED_STOCK_ROUTE,
				CONCAT(MATERIAL_PAST_DUE_SO_QTY, '->', T.COMPONENT_PAST_DUE_SO_QTY)   AS PAST_DUE_SO_QTY_ROUTE,
				CONCAT(MATERIAL_PAST_DUE_SO_QTY_NON_BLOCK, '->',
				T.COMPONENT_PAST_DUE_SO_QTY_NON_BLOCK)                         AS PAST_DUE_SO_QTY_NON_BLOCK_ROUTE,
				CONCAT(MATERIAL_TOTAL_OPEN_SO_QTY, '->',
				T.COMPONENT_TOTAL_OPEN_SO_QTY)                                 AS TOTAL_OPEN_SO_QTY_ROUTE,
				CONCAT(MATERIAL_WITHIN_LT_SO_QTY, '->', T.COMPONENT_WITHIN_LT_SO_QTY) AS WITHIN_LT_SO_QTY_ROUTE,
				CONCAT(MATERIAL_WITHOUT_LT_SO_QTY, '->',
				T.COMPONENT_WITHOUT_LT_SO_QTY)                                 AS WITHOUT_LT_SO_QTY_ROUTE,
				CONCAT(MATERIAL_TOTAL_OPEN_SO_QTY_NON_BLOCK, '->',
				T.COMPONENT_TOTAL_OPEN_SO_QTY_NON_BLOCK)                       AS TOTAL_OPEN_SO_QTY_NON_BLOCK_ROUTE,
				CONCAT(MATERIAL_WITHIN_LT_SO_NON_BLOCK, '->',
				T.COMPONENT_WITHIN_LT_SO_NON_BLOCK)                            AS WITHIN_LT_SO_NON_BLOCK_ROUTE,
				CONCAT(MATERIAL_WITHOUT_LT_SO_NON_BLOCK, '->',
				T.COMPONENT_WITHOUT_LT_SO_NON_BLOCK)                           AS WITHOUT_LT_SO_NON_BLOCK_ROUTE,
				CONCAT(MATERIAL_OPEN_PO_QTY, '->', T.COMPONENT_OPEN_PO_QTY)           AS OPEN_PO_QTY_ROUTE,
				CONCAT(MATERIAL_OPEN_SO_QTY, '->', T.COMPONENT_OPEN_SO_QTY)           AS OPEN_SO_QTY_ROUTE,
				CONCAT(MATERIAL_ORDER_RESERVATION_QTY, '->',
				T.COMPONENT_ORDER_RESERVATION_QTY)                             AS ORDER_RESERVATION_QTY_ROUTE,
				CONCAT(MATERIAL_PASTDUE_PO_QTY, '->', T.COMPONENT_PASTDUE_PO_QTY)     AS PASTDUE_PO_QTY_ROUTE,
				CONCAT(MATERIAL_PO_AB, '->', T.COMPONENT_PO_AB)                       AS OPEN_PO_AB_ROUTE,
				CONCAT(MATERIAL_PO_LA, '->', T.COMPONENT_PO_LA)                       AS OPEN_PO_LA_ROUTE,
				CONCAT(MATERIAL_PASTDUE_ORDER_RESERVATION, '->',
				T.COMPONENT_PASTDUE_ORDER_RESERVATION)                         AS PASTDUE_ORDER_RESERVATION_ROUTE,
				CONCAT(MATERIAL_ORDER_RESERVATION_QTY_WITHIN_7_DAYS, '->',
				COMPONENT_ORDER_RESERVATION_QTY_WITHIN_7_DAYS)                 AS ORDER_RESERVATION_QTY_WITHIN_7_DAYS_ROUTE,
				CONCAT(
				MATERIAL_ORDER_RESERVATION_QTY_WITHOUT_7_DAYS, '->',
				COMPONENT_ORDER_RESERVATION_QTY_WITHOUT_7_DAYS)               AS ORDER_RESERVATION_QTY_WITHOUT_7_DAYS_ROUTE,
				[toString(MATERIAL)]          AS path
				FROM scpc.`CRITICAL_BOM_V` T
				INNER JOIN (SELECT MATERIAL, PLANT_CODE
				FROM scpc.CRITICAL_BOM_MANUAL
				WHERE USER_ID = #{session.userid, jdbcType=VARCHAR}) M
				ON M.MATERIAL = T.MATERIAL AND M.PLANT_CODE = T.PLANT_CODE
				WHERE PLANT_CODE IN
				<foreach collection="plant" item="plantItem" open="(" separator="," close=")">
					#{plantItem, jdbcType=VARCHAR}
				</foreach>

				UNION ALL

				SELECT c.BOM_COMPONENT AS ROOT_MATERIAL,
				c.MATERIAL                      AS PARENT_MATERIAL,
				c.BOM_COMPONENT                 AS CHILD_MATERIAL,
				concat(ROUTE, '->', c.BOM_COMPONENT) AS ROUTE,
				concat(PLANT_ROUTE, '->', c.PLANT_CODE),
				concat(COMPONENT_QTY_ROUTE, '->', c.COMPONENT_QTY),
				p.TOP,
				p.TOP_PLANT_CODE,
				c.BOM_COMPONENT,
				c.PLANT_CODE,
				CASE
				WHEN p.TRANSFER_NUMBER ='' AND c.PO_MATERIAL !=''
				THEN c.BOM_COMPONENT || '&lt;->' || c.PO_MATERIAL
				WHEN p.TRANSFER_NUMBER !='' AND c.PO_MATERIAL !=''
				THEN '/' || c.BOM_COMPONENT || '&lt;->' || c.PO_MATERIAL
				ELSE p.TRANSFER_NUMBER END  AS TRANSFER_NUMBER,
				CONCAT(PLANT_TYPE_ROUTE, '->', COMPONENT_PLANT_TYPE),
				CONCAT(CLUSTER_NAME_ROUTE, '->', COMPONENT_CLUSTER_NAME),
				CONCAT(ENTITY_ROUTE, '->', COMPONENT_ENTITY),
				CONCAT(PRODUCT_LINE_ROUTE, '->', COMPONENT_PRODUCT_LINE),
				CONCAT(PRODUCTION_LINE_ROUTE, '->', COMPONENT_PRODUCTION_LINE),
				CONCAT(LOCAL_PRODUCT_LINE_ROUTE, '->', COMPONENT_LOCAL_PRODUCT_LINE),
				CONCAT(LOCAL_PRODUCT_FAMILY_ROUTE, '->', COMPONENT_LOCAL_PRODUCT_FAMILY),
				CONCAT(LOCAL_PRODUCT_SUBFAMILY_ROUTE, '->', COMPONENT_LOCAL_PRODUCT_SUBFAMILY),
				CONCAT(HYPER_CARE_LEVEL_ROUTE, '->', COMPONENT_HYPER_CARE_LEVEL),
				CONCAT(MRP_CONTROLLER_ROUTE, '->', COMPONENT_MRP_CONTROLLER),
				CONCAT(STOCKING_POLICY_ROUTE, '->', COMPONENT_STOCKING_POLICY),
				CONCAT(VENDOR_CODE_ROUTE, '->', COMPONENT_VENDOR_CODE),
				CONCAT(VENDOR_NAME_ROUTE, '->', COMPONENT_VENDOR_NAME),
				CONCAT(VENDOR_SHORT_NAME_ROUTE, '->', COMPONENT_VENDOR_SHORT_NAME),
				CONCAT(VENDOR_FULL_NAME_ROUTE, '->', COMPONENT_VENDOR_FULL_NAME),
				CONCAT(VENDOR_PARENT_NAME_ROUTE, '->', COMPONENT_VENDOR_PARENT_NAME),
				CONCAT(VENDOR_PARENT_CODE_ROUTE, '->', COMPONENT_VENDOR_PARENT_CODE),
				CONCAT(VENDOR_MATERIAL_ROUTE, '->', COMPONENT_VENDOR_MATERIAL),
				CONCAT(SOURCE_CATEGORY_ROUTE, '->', COMPONENT_SOURCE_CATEGORY),
				CONCAT(PLANNED_DELIV_TIME_ROUTE, '->', COMPONENT_PLANNED_DELIV_TIME),
				CONCAT(LT_RANGE_ROUTE, '->', COMPONENT_LT_RANGE),
				CONCAT(UNIT_COST_ROUTE, '->', COMPONENT_UNIT_COST),
				CONCAT(SAFETY_STOCK_ROUTE, '->', COMPONENT_SAFETY_STOCK),
				CONCAT(AMU_ONE_MM_ROUTE, '->', COMPONENT_AMU_ONE_MM),
				CONCAT(AVG_MONTHLY_ORDER_INTAKE_ROUTE, '->', COMPONENT_AVG_MONTHLY_ORDER_INTAKE),
				CONCAT(AMF_ONE_MM_ROUTE, '->', COMPONENT_AMF_ONE_MM),
				CONCAT(COV_ROUTE, '->', COMPONENT_COV),
				CONCAT(SS2_ROUTE, '->', COMPONENT_SS2),
				CONCAT(SS3_ROUTE, '->', COMPONENT_SS3),
				CONCAT(CALCULATED_ABC_ROUTE, '->', COMPONENT_CALCULATED_ABC),
				CONCAT(CALCULATED_FMR_ROUTE, '->', COMPONENT_CALCULATED_FMR),
				CONCAT(NEW_PRODUCTS_ROUTE, '->', COMPONENT_NEW_PRODUCTS),
				CONCAT(PROCUREMENT_TYPE_ROUTE, '->', COMPONENT_PROCUREMENT_TYPE),
				CONCAT(FOLLOW_UP_MATERIAL_ROUTE, '->', COMPONENT_FOLLOW_UP_MATERIAL),
				CONCAT(PRODN_SUPERVISOR_ROUTE, '->', COMPONENT_PRODN_SUPERVISOR),
				CONCAT(IN_HOUSE_PRODN_LT_ROUTE, '->', COMPONENT_IN_HOUSE_PRODN_LT),
				CONCAT(STARS_CODE_ROUTE, '->', COMPONENT_STARS_CODE),
				CONCAT(LAST_SO_SALES_DATE_ROUTE, '->', COMPONENT_LAST_SO_SALES_DATE),
				CONCAT(NONE_SALES_DURATION_ROUTE, '->', COMPONENT_NONE_SALES_DURATION),
				CONCAT(NONE_SALES_PERIOD_ROUTE, '->', COMPONENT_NONE_SALES_PERIOD),
				CONCAT(GRA_ROUTE, '->', COMPONENT_GRA),
				CONCAT(COMMODITY_CODE_ROUTE, '->', COMPONENT_COMMODITY_CODE),
				CONCAT(STARS_DESCRIPTION_ROUTE, '->', COMPONENT_STARS_DESCRIPTION),
				CONCAT(PLANNING_ALIVE_STATUS_ROUTE, '->', COMPONENT_PLANNING_ALIVE_STATUS),
				CONCAT(XBOARD_ROUTE, '->', COMPONENT_XBOARD),
				CONCAT(RES_COV_STK_ROUTE, '->', COMPONENT_RES_COV_STK),
				CONCAT(RES_COV_STK_RANGE_ROUTE, '->', COMPONENT_RES_COV_STK_RANGE),
				CONCAT(RES_COV_STK_LA_ROUTE, '->', COMPONENT_RES_COV_STK_LA),
				CONCAT(RES_COV_STK_LA_RANGE_ROUTE, '->', COMPONENT_RES_COV_STK_LA_RANGE),
				CONCAT(RES_COV_STK_OPO_ROUTE, '->', COMPONENT_RES_COV_STK_OPO),
				CONCAT(RES_COV_STK_OPO_RANGE_ROUTE, '->', COMPONENT_RES_COV_STK_OPO_RANGE),
				CONCAT(FIRST_CONSUMPTION_RANGE_ROUTE, '->', COMPONENT_FIRST_CONSUMPTION_RANGE),
				CONCAT(FIRST_PO_CREATE_RANGE_ROUTE, '->', COMPONENT_FIRST_PO_CREATE_RANGE),
				CONCAT(FIRST_SO_CREATE_RANGE_ROUTE, '->', COMPONENT_FIRST_SO_CREATE_RANGE),
				CONCAT(LAST_SO_SALES_RANGE_ROUTE, '->', COMPONENT_LAST_SO_SALES_RANGE),
				CONCAT(PRD_BOM_COMPONENT_ROUTE, '->', COMPONENT_PRD_BOM_COMPONENT),
				CONCAT(FIRST_CONS_DATE_ROUTE, '->', COMPONENT_FIRST_CONS_DATE),
				CONCAT(FIRST_SO_DATE_ROUTE, '->', COMPONENT_FIRST_SO_DATE),
				CONCAT(FIRST_PO_DATE_ROUTE, '->', COMPONENT_FIRST_PO_DATE),
				CONCAT(COV_RANGE_ROUTE, '->', COMPONENT_COV_RANGE),
				CONCAT(RISK_LEVEL_ROUTE, '->', COMPONENT_RISK_LEVEL),
				CONCAT(ADU_END_ORDER_ROUTE, '->', COMPONENT_ADU_END_ORDER),
				CONCAT(STDDEV_END_ORDER_ROUTE, '->', COMPONENT_STDDEV_END_ORDER),
				CONCAT(COV_END_ORDER_ROUTE, '->', COMPONENT_COV_END_ORDER),
				CONCAT(COV_RANGE_END_ORDER_ROUTE, '->', COMPONENT_COV_RANGE_END_ORDER),
				CONCAT(NUM_OF_WHERE_USE_ROUTE, '->', COMPONENT_NUM_OF_WHERE_USE),
				CONCAT(NUM_OF_WHERE_USE_RANGE_ROUTE, '->', COMPONENT_NUM_OF_WHERE_USE_RANGE),
				CONCAT(PRODUCT_GROUP_A_ROUTE, '->', COMPONENT_PRODUCT_GROUP_A),
				CONCAT(PRODUCT_GROUP_B_ROUTE, '->', COMPONENT_PRODUCT_GROUP_B),
				CONCAT(PRODUCT_GROUP_C_ROUTE, '->', COMPONENT_PRODUCT_GROUP_C),
				CONCAT(PRODUCT_GROUP_D_ROUTE, '->', COMPONENT_PRODUCT_GROUP_D),
				CONCAT(PRODUCT_GROUP_E_ROUTE, '->', COMPONENT_PRODUCT_GROUP_E),
				CONCAT(TOTAL_STOCK_QTY_ROUTE, '->', COMPONENT_TOTAL_STOCK_QTY),
				CONCAT(UU_STOCK_ROUTE, '->', COMPONENT_UU_STOCK),
				CONCAT(QI_STOCK_QTY_ROUTE, '->', COMPONENT_STOCK_IN_QI),
				CONCAT(BLOCKED_STOCK_ROUTE, '->', COMPONENT_BLOCKED_STOCK),
				CONCAT(PAST_DUE_SO_QTY_ROUTE, '->', COMPONENT_PAST_DUE_SO_QTY),
				CONCAT(PAST_DUE_SO_QTY_NON_BLOCK_ROUTE, '->', COMPONENT_PAST_DUE_SO_QTY_NON_BLOCK),
				CONCAT(TOTAL_OPEN_SO_QTY_ROUTE, '->', COMPONENT_TOTAL_OPEN_SO_QTY),
				CONCAT(WITHIN_LT_SO_QTY_ROUTE, '->', COMPONENT_WITHIN_LT_SO_QTY),
				CONCAT(WITHOUT_LT_SO_QTY_ROUTE, '->', COMPONENT_WITHOUT_LT_SO_QTY),
				CONCAT(TOTAL_OPEN_SO_QTY_NON_BLOCK_ROUTE, '->', COMPONENT_TOTAL_OPEN_SO_QTY_NON_BLOCK),
				CONCAT(WITHIN_LT_SO_NON_BLOCK_ROUTE, '->', COMPONENT_WITHIN_LT_SO_NON_BLOCK),
				CONCAT(WITHOUT_LT_SO_NON_BLOCK_ROUTE, '->', COMPONENT_WITHOUT_LT_SO_NON_BLOCK),
				CONCAT(OPEN_PO_QTY_ROUTE, '->', COMPONENT_OPEN_PO_QTY),
				CONCAT(OPEN_SO_QTY_ROUTE, '->', COMPONENT_OPEN_SO_QTY),
				CONCAT(ORDER_RESERVATION_QTY_ROUTE, '->', COMPONENT_ORDER_RESERVATION_QTY),
				CONCAT(PASTDUE_PO_QTY_ROUTE, '->', COMPONENT_PASTDUE_PO_QTY),
				CONCAT(OPEN_PO_AB_ROUTE, '->', COMPONENT_PO_AB),
				CONCAT(OPEN_PO_LA_ROUTE, '->', COMPONENT_PO_LA),
				CONCAT(PASTDUE_ORDER_RESERVATION_ROUTE, '->', COMPONENT_PASTDUE_ORDER_RESERVATION),
				CONCAT(ORDER_RESERVATION_QTY_WITHIN_7_DAYS_ROUTE, '->',
				COMPONENT_ORDER_RESERVATION_QTY_WITHIN_7_DAYS),
				CONCAT(ORDER_RESERVATION_QTY_WITHOUT_7_DAYS_ROUTE, '->',
				COMPONENT_ORDER_RESERVATION_QTY_WITHOUT_7_DAYS),
				arrayConcat(p.path, [toString(c.BOM_COMPONENT)])
				FROM scpc.CRITICAL_BOM_V AS c
				JOIN bom_tree AS p
				ON c.MATERIAL = p.ROOT_MATERIAL
				WHERE NOT has(arraySlice(p.path, 1, length(p.path)-1), toString(c.MATERIAL))
				AND PLANT_CODE IN
				<foreach collection="plant" item="plantItem" open="(" separator="," close=")">
					#{plantItem, jdbcType=VARCHAR}
				</foreach>)
				SELECT DISTINCT *
				FROM bom_tree
			</otherwise>
		</choose>
	</sql>

	<select id="queryReport1" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
		SELECT * FROM (
			<include refid="queryReport1Sql"/>
		) PAGE
		<if test="_page.sort != null and _page.sort != ''.toString()">
			ORDER BY ${_page.sort}
		</if>
		<if test="_page.pagging">
			LIMIT #{_page.length,jdbcType=INTEGER} OFFSET #{_page.start,jdbcType=INTEGER}
		</if>
		<if test="not _page.pagging">
			LIMIT #{_page.maxRows,jdbcType=INTEGER}
		</if>
	</select>

	<select id="queryReport1Count" parameterType="java.util.Map" resultType="java.lang.Integer">
		SELECT COUNT(1) FROM (
			<include refid="queryReport1Sql"/>
		) PAGE
	</select>

	<insert id="saveReport1Details">
		<!-- ClickHouse implementation for saveReport1Details -->
		<!-- This method may be used for saving report details, implementation depends on business requirements -->
		INSERT INTO CRITICAL_BOM_MANUAL_LOG (USER_ID, ACTION, CREATE_DATE)
		VALUES (#{session.userid, jdbcType=VARCHAR}, 'SAVE_REPORT1_DETAILS', now())
	</insert>

	<sql id="queryReport2Sql">
		select CONCAT(MATERIAL, '|', PLANT_CODE) AS ROW_ID, MATERIAL, PLANT_CODE from CRITICAL_BOM_MANUAL
		       WHERE USER_ID = #{session.userid, jdbcType=VARCHAR}
	</sql>

	<select id="queryReport2Count" parameterType="java.util.Map" resultType="java.lang.Integer">
		SELECT COUNT(1) FROM (
			<include refid="queryReport2Sql"/>
		) PAGE
	</select>

	<select id="queryReport2" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
		SELECT * FROM (
			<include refid="queryReport2Sql"/>
		) PAGE
		<if test="_page.sort != null and _page.sort != ''.toString()">
			ORDER BY ${_page.sort}
		</if>
		<if test="_page.pagging">
			LIMIT #{_page.length,jdbcType=INTEGER} OFFSET #{_page.start,jdbcType=INTEGER}
		</if>
		<if test="not _page.pagging">
			LIMIT #{_page.maxRows,jdbcType=INTEGER}
		</if>
	</select>

	<select id="downloadReport2" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
		SELECT PLANT_CODE,MATERIAL FROM CRITICAL_BOM_MANUAL WHERE USER_ID = #{session.userid, jdbcType=VARCHAR}
		<if test="_page.sort != null and _page.sort != ''.toString()">
			ORDER BY ${_page.sort}
		</if>
		<if test="_page.pagging">
			LIMIT #{_page.length,jdbcType=INTEGER} OFFSET #{_page.start,jdbcType=INTEGER}
		</if>
		<if test="not _page.pagging">
			LIMIT #{_page.maxRows,jdbcType=INTEGER}
		</if>
	</select>

	<update id="updateReport2ByTable">
		ALTER TABLE CRITICAL_BOM_MANUAL UPDATE
		<foreach collection="updates" item="col" separator=",">
			${col.key} = #{col.value,jdbcType=VARCHAR}
		</foreach>
		<if test="updates != null and updates.size() > 0">,</if>
		UPDATE_BY = #{userid,jdbcType=VARCHAR},
		UPDATE_DATE = now()
		WHERE MATERIAL = splitByChar('|', #{pk,jdbcType=VARCHAR})[1]
		  AND PLANT_CODE = splitByChar('|', #{pk,jdbcType=VARCHAR})[2]
		  AND USER_ID = #{userid,jdbcType=VARCHAR}
	</update>

	<insert id="createReport2ByTable">
		INSERT INTO CRITICAL_BOM_MANUAL
		(
		<foreach collection="headers" item="header" separator=",">
			${header}
		</foreach>, USER_ID, CREATE_BY, CREATE_DATE
		)
		VALUES
		<foreach collection="inserts" item="list" separator=",">
			(
			<foreach collection="headers" item="header" separator=",">
				#{list.${header}, jdbcType=VARCHAR}
			</foreach>,#{userid,jdbcType=VARCHAR}, #{userid,jdbcType=VARCHAR}, now()
			)
		</foreach>
	</insert>

	<delete id="deleteReport2ByTable">
		ALTER TABLE CRITICAL_BOM_MANUAL DELETE WHERE
		<foreach collection="deletes" open="(" close=")" separator=" OR " item="item">
			(MATERIAL = splitByChar('|', #{item, jdbcType=VARCHAR})[1] AND PLANT_CODE = splitByChar('|', #{item, jdbcType=VARCHAR})[2])
		</foreach>
		AND USER_ID = #{userid,jdbcType=VARCHAR}
	</delete>

	<insert id="insertReport2Data">
		INSERT INTO CRITICAL_BOM_MANUAL
		(MATERIAL, PLANT_CODE,USER_ID,CREATE_BY,CREATE_DATE)
		VALUES
		<foreach collection="list" separator="," item="item">
			(
			#{item.MATERIAL, jdbcType=VARCHAR},
			#{item.PLANT_CODE, jdbcType=VARCHAR},
			#{item.USER_ID,jdbcType=VARCHAR},
			#{item.USER_ID,jdbcType=VARCHAR},
			now()
			)
		</foreach>
	</insert>

	<delete id="queryReport2DeplicateRows">
		ALTER TABLE CRITICAL_BOM_MANUAL DELETE WHERE (MATERIAL, PLANT_CODE, USER_ID, CREATE_DATE) NOT IN (
			SELECT MATERIAL, PLANT_CODE, USER_ID, min(CREATE_DATE)
			FROM CRITICAL_BOM_MANUAL
			GROUP BY MATERIAL, PLANT_CODE, USER_ID
		)
	</delete>

	<update id="mergeReport2Data">
		ALTER TABLE DEMAND_SUPPLY_CRITICAL_MATERIAL_MANUAL DELETE WHERE USER_ID = #{userid, jdbcType=VARCHAR}
	</update>

	<insert id="insertMergedReport2Data">
		INSERT INTO DEMAND_SUPPLY_CRITICAL_MATERIAL_MANUAL
			(MATERIAL, PLANT_CODE, USER_ID, CREATE_BY, CREATE_DATE)
		SELECT MATERIAL, PLANT_CODE, #{userid,jdbcType=VARCHAR}, #{userid,jdbcType=VARCHAR}, now() FROM scpc.CRITICAL_BOM_MANUAL WHERE USER_ID = #{userid,jdbcType=VARCHAR}
	</insert>

	<select id="queryAllPlant" resultType="java.lang.String">
		SELECT DISTINCT PLANT_CODE FROM MR3_PLANT_MASTER_DATA
	</select>

	<select id="queryReport3" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
		SELECT MATERIAL, PLANT_CODE, USER_ID, CREATE_DATE
		FROM scpc.CRITICAL_BOM_MANUAL
		WHERE USER_ID = #{session.userid, jdbcType=VARCHAR}
		<if test="_page.sort != null and _page.sort != ''.toString()">
			ORDER BY ${_page.sort}
		</if>
		<if test="_page.pagging">
			LIMIT #{_page.length,jdbcType=INTEGER} OFFSET #{_page.start,jdbcType=INTEGER}
		</if>
		<if test="not _page.pagging">
			LIMIT #{_page.maxRows,jdbcType=INTEGER}
		</if>
	</select>

	<select id="querySankeyChart" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
		SELECT MATERIAL, BOM_COMPONENT, COMPONENT_QTY
		FROM CRITICAL_BOM_V
		WHERE PLANT_CODE IN
		<foreach collection="plantList" item="plant" open="(" separator="," close=")">
			#{plant, jdbcType=VARCHAR}
		</foreach>
		AND MATERIAL = #{material, jdbcType=VARCHAR}
		ORDER BY MATERIAL, BOM_COMPONENT
	</select>

	<insert id="insertBomReport2">
		INSERT INTO CB_BCDC_INPUT_BOM_CLICK
		(BATCH_ID,
		 MATERIAL,
		 BOM_COMPONENT,
		 USAGE
		)
		SELECT #{batchId, jdbcType=VARCHAR},
			   T.MATERIAL,
			   T.BOM_COMPONENT,
			   T.COMPONENT_QTY
		FROM scpc.CRITICAL_BOM_V T
		WHERE
		<if test="plant != null and plant.size > 0">
		T.PLANT_CODE IN
		<foreach collection="plant" item="p" open="(" separator="," close=")">
			#{p, jdbcType=VARCHAR}
		</foreach>
		</if>
	</insert>

</mapper>
