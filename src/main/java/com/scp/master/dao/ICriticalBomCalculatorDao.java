package com.scp.master.dao;

import com.scp.simulation.bean.*;
import com.scp.toolbox.bean.TreeData;
import com.starter.context.bean.scptable.ScpTableCell;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Mapper
public interface ICriticalBomCalculatorDao {

    List<TreeData> queryUserBatchList(@Param("userid") String userid, @Param("authType") String authType,List<String> batch_id);


    void saveBestCanDoLog(BestCanDoLog log);

    List<Map<String, String>> queryFilterList();

    List<Map<String, String>> queryTaskInfo(String userid, String authType,List<String> batch_id);

    int copyPOData(Map<String, Object> parameterMap);

    int copyMOData(Map<String, Object> parameterMap);

    int copySOData(Map<String, Object> parameterMap);

    void insertLog(Map<String, Object> parameterMap);

    int queryStartCount(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryStart(Map<String, Object> parameterMap);

    int queryReport1Count(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryReport1(Map<String, Object> parameterMap);

    int queryReport2Count(Map<String, Object> parameterMap);

    void insertBomReport2(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryReport2(Map<String, Object> parameterMap);

    int queryReport11Count(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryReport11(Map<String, Object> parameterMap);

    int queryReport3Count(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryReport3(Map<String, Object> parameterMap);

    int queryReport9Count(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryReport9(Map<String, Object> parameterMap);


    List<String> queryExecuteLogs(Map<String, Object> parameterMap);

    void deleteBatch(Map<String, Object> parameterMap);

    int queryBatchCount(Map<String, Object> parameterMap);

    int querySimulateCount(Map<String, Object> parameterMap);

    int createStartByTable(List<String> headers, List<Map<String, Object>> creates, String batchId);

    int deleteStartByTable(List<String> deletes, String batchId);

    int updateStartByTable(String rowid, List<ScpTableCell> updates, String batchId);

    int createReport1ByTable(List<String> headers, List<Map<String, Object>> creates, String batchId);

    int deleteReport1ByTable(List<String> deletes, String batchId);

    int updateReport1ByTable(String rowid, List<ScpTableCell> updates, String batchId);

    int createReport2ByTable(List<String> headers, List<Map<String, Object>> creates, String batchId);

    int deleteReport2ByTable(List<String> deletes, String batchId);

    int updateReport2ByTable(String rowid, List<ScpTableCell> updates, String batchId);

    int createReport11ByTable(List<String> headers, List<Map<String, Object>> creates, String batchId);

    int deleteReport11ByTable(List<String> deletes, String batchId);

    int updateReport11ByTable(String rowid, List<ScpTableCell> updates, String batchId);

    int createReport3ByTable(List<String> headers, List<Map<String, Object>> creates, String batchId);

    int deleteReport3ByTable(List<String> deletes, String batchId);

    int updateReport3ByTable(String rowid, List<ScpTableCell> updates, String batchId);

    int createReport9ByTable(List<String> headers, List<Map<String, Object>> creates, String batchId);

    int deleteReport9ByTable(List<String> deletes, String batchId);

    int updateReport9ByTable(String rowid, List<ScpTableCell> updates, String batchId);

    int createReport7ByTable(List<String> headers, List<Map<String, Object>> creates, String batchId);

    int deleteReport7ByTable(List<String> deletes, String batchId);

    int updateReport7ByTable(String rowid, List<ScpTableCell> updates, String batchId);

    String queryBatchParams(String batchId);

    void deleteStartData(String batchId);

    void insertStartData(@Param("batchId") String batchId, @Param("list") List<DemandInput> data);

    void deleteReport1Data(String batchId);

    void insertReport1Data(@Param("batchId") String batchId, @Param("list") List<DemandInput> data);

    void deleteReport2Data(String batchId);

    void insertReport2Data(@Param("batchId") String batchId, @Param("list") List<BomInput> data);

    void deleteReport11Data(String batchId);

    void insertReport11Data(@Param("batchId") String batchId, @Param("list") List<BomInput> data);

    void deleteReport3Data(String batchId);

    void insertReport3Data(@Param("batchId") String batchId, @Param("list") List<RecourseInput> data);

    void deleteReport9Data(String batchId);

    void insertReport9Data(@Param("batchId") String batchId, @Param("list") List<RecourseInput> data);


    void deleteReport7Data(String batchId);

    void insertReport7Data(@Param("batchId") String batchId, @Param("list") List<SubstitutionInput> data);

    int queryExecutingCount(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryTaskQueue(Map<String, Object> parameterMap);

    int queryReport4Count(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryReport4(Map<String, Object> parameterMap);

    int queryReport5Count(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryReport5(Map<String, Object> parameterMap);

    int queryReport10Count(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryReport10(Map<String, Object> parameterMap);

    int queryReport8Count(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryReport8(Map<String, Object> parameterMap);

    int queryReport6Count(Map<String, Object> parameterMap);

    int queryReport7Count(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryReport6(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryReport7(Map<String, Object> parameterMap);

    String queryAuthDetails(String userid, @Param("menuCode") String menuCode);

    Map<String, Object> queryBatchInfo(Map<String, Object> parameterMap);

    List<String> queryAvailablePlants();

    List<String> initExistsGroup(String userid, String authType);

    int queryTaskQueueCount(Map<String, Object> parameterMap);

    void updateSimulateStep(Map<String, Object> parameterMap);

    void updatePlant(String plant, String batchId);

    Map<String, Object> queryBatchStepInfo(Map<String, Object> parameterMap);

    List<String> queryBatchStepName(Map<String, Object> stepInfo);

    List<BestCanDoCompare1> compareReport1(Map<String, Object> parameterMap);

    int compareReport1DetailsCount(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> compareReport1Details(Map<String, Object> parameterMap);

    List<String> compareReport1legend(Map<String, Object> parameterMap);

    List<BestCanDoCompare2Bean> compareReport2(Map<String, Object> parameterMap);

    int compareReport2DetailsCount(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> compareReport2Details(Map<String, Object> parameterMap);

    List<BestCanDoCompare1> compareReport3(Map<String, Object> parameterMap);

    List<String> compareReport3legend(Map<String, Object> parameterMap);

    int compareReport3DetailsCount(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> compareReport3Details(Map<String, Object> parameterMap);

    List<BestCanDoCompare1> compareReport4Left(Map<String, Object> parameterMap);

    List<BestCanDoCompare1> compareReport4Right(Map<String, Object> parameterMap);

    List<String> compareReport4legend(Map<String, Object> parameterMap);

    int compareReport4DetailsCount(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> compareReport4Details(Map<String, Object> parameterMap);

    Map<String, BigDecimal> compareReportOverview(Map<String, Object> parameterMap);

    int compareOverviewDetailsCount(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> compareOverviewDetails(Map<String, Object> parameterMap);

    void shareCondition(Map<String, Object> parameterMap);

    List<String> queryShareAuth(String userid);

}
