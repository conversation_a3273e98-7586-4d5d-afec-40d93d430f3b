package com.scp.master.dao;

import com.starter.context.bean.scptable.ScpTableCell;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * CriticalBom ClickHouse数据访问接口
 * 
 * <AUTHOR>
 */
@Mapper
public interface ICriticalBomClickHouseDao {

    List<Map<String, String>> queryCascader();

    int queryApplicationAuth(@Param("userid") String userid);

    List<LinkedHashMap<String, Object>> queryReport1(Map<String, Object> parameterMap);

    int queryReport1Count(Map<String, Object> parameterMap);

    void saveReport1Details(Map<String, Object> parameterMap);

    int queryReport2Count(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryReport2(Map<String, Object> parameterMap);

    List<String> queryAvailablePlants();

    int createReport2ByTable(List<String> headers, List<Map<String, Object>> inserts, String userid);

    int deleteReport2ByTable(List<String> deletes, String userid);

    int updateReport2ByTable(String pk, List<ScpTableCell> updates, String userid);

    void insertReport2Data(List<Map<String, Object>> data);

    void mergeReport2Data(String userid);

    void insertMergedReport2Data(String userid);

    List<LinkedHashMap<String, Object>> queryReport3(Map<String, Object> parameterMap);

    List<String> queryAllPlant();

    void queryReport2DeplicateRows();

    List<LinkedHashMap<String, Object>> querySankeyChart(Map<String, Object> parameterMap);

    void insertBomReport2(Map<String, Object> parameterMap);
}
