<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.scp.demand.dao.IOpenPmsStructureDao">
    <sql id="openPmsFilter">
        <if test="_filters != null and _filters != ''.toString()">
            and ${_filters}
        </if>
        <if test="scopeFilter != null and scopeFilter != ''.toString()">
            and ${scopeFilter}
        </if>
        <if test="treePathFilter != null and treePathFilter != ''.toString()">
            and ${treePathFilter}
        </if>
        <if test="personalFilters != null and personalFilters != ''.toString()">
            and ${personalFilters}
        </if>
        <if test="dateColumnRange != null and dateColumnRange.size() > 0">
            AND T.${dateColumn} BETWEEN TO_DATE(#{dateColumnRange[0], jdbcType=VARCHAR}, 'yyyy/mm/dd')
            AND TRUNC(TO_DATE(#{dateColumnRange[1], jdbcType=VARCHAR}, 'yyyy/mm/dd'), 'dd')
        </if>
    </sql>

    <sql id="openPmsHistFilter">
        <if test="_filters != null and _filters != ''.toString()">
            and ${_filters}
        </if>
        <if test="scopeFilter != null and scopeFilter != ''.toString()">
            and ${scopeFilter}
        </if>
        <if test="treePathFilter != null and treePathFilter != ''.toString()">
            and ${treePathFilter}
        </if>
        <if test="personalFilters != null and personalFilters != ''.toString()">
            and ${personalFilters}
        </if>
        <if test="dateColumnRange != null and dateColumnRange.size() > 0">
            AND T.${dateColumn} BETWEEN TO_DATE(#{dateColumnRange[0], jdbcType=VARCHAR}, 'yyyy/mm/dd')
            AND TRUNC(TO_DATE(#{dateColumnRange[1], jdbcType=VARCHAR}, 'yyyy/mm/dd'), 'dd')
        </if>
    </sql>
    <select id="queryCascader" resultType="java.util.Map">
        SELECT NAME, CATEGORY FROM OPEN_PMS_STRUCTURE_FILTER_V T ORDER BY CATEGORY, NLSSORT(decode(t.NAME, 'Others', 'zzz', t.NAME), 'NLS_SORT = SCHINESE_PINYIN_M')
    </select>

    <resultMap id="columnMap" type="java.lang.String">
        <result column="COLUMN_NAME"/>
    </resultMap>

    <select id="queryDateColumns" resultMap="columnMap" resultType="java.util.List">
        SELECT COLUMN_NAME
        FROM USER_TAB_COLS
        WHERE TABLE_NAME IN ('OPEN_PMS_STRUCTURE_V', 'OPEN_PMS_STRUCTURE_HIST')
          AND DATA_TYPE = 'DATE'
        GROUP BY COLUMN_NAME
        HAVING COUNT(1) = 2
        ORDER BY COLUMN_NAME
    </select>

    <resultMap id="report2ResultMap" type="com.scp.demand.bean.OpenPmsReport2Bean">
        <result property="category1" column="CATEGORY1"/>
        <result property="category2" column="CATEGORY2"/>
        <result property="category3" column="CATEGORY3"/>
        <result property="category4" column="CATEGORY4"/>
        <result property="category5" column="CATEGORY5"/>
        <result property="value" column="value"/>
        <association property="tooltips" javaType="com.scp.demand.bean.OpenPmsReport2Tooltips">
            <result property="OPEN_PMS_QTY" column="OPEN_PMS_QTY"/>
        </association>
    </resultMap>

    <select id="queryReport2" resultMap="report2ResultMap">
        WITH BASE AS (
            SELECT /*+ parallel(t 6) */ * FROM ${SCPA.OPEN_PMS_STRUCTURE_V} T
            <where>
                <include refid="openPmsFilter"/>
            </where>
        )
        SELECT
        NVL(${level1}, 'Others') AS CATEGORY1,
        NVL(${level2}, 'Others') AS CATEGORY2,
        NVL(${level3}, 'Others') AS CATEGORY3,
        <if test="level4 != null and level4 != ''.toString()">
            NVL(${level4}, 'Others') AS CATEGORY4,
        </if>
        <if test="level5 != null and level5 != ''.toString()">
            NVL(${level5}, 'Others') AS CATEGORY5,
        </if>
        ${valueColumn} AS VALUE
        <if test="tooltipsColumns != null and tooltipsColumns != ''.toString()">
            ,${tooltipsColumns}
        </if>
        FROM BASE t
        <where>
            <include refid="openPmsFilter"/>
        </where>
        GROUP BY
        ${level1}, ${level2}, ${level3}
        <if test="level4 != null and level4 != ''.toString()">,${level4}</if>
        <if test="level5 != null and level5 != ''.toString()">,${level5}</if>
    </select>

    <select id="queryReport3" resultType="java.util.Map">
        WITH BASE AS (
            SELECT /*+ parallel(t 6) */ * FROM ${SCPA.OPEN_PMS_STRUCTURE_V} T
            <where>
                <include refid="openPmsFilter"/>
            </where>
        )
        SELECT ${report3GroupBy} AS "name",
               ${valueColumn} AS "value"
        FROM BASE T
       GROUP BY ${report3GroupBy}
       ORDER BY DECODE(
        ${report3GroupBy},
            '0D', 'A',
            '0-1D', 'B',
            '1-2D', 'C',
            '2-3D', 'D',
            '3-7D', 'E',
            '5-7D', 'F',
            '3-4D', 'G',
            '1-2W', 'H',
            '2-4W', 'I',
            '1-2M', 'J',
            '2-3M', 'K',
            '3-6M', 'L',
            '6-12M', 'M',
            '6-1Y', 'N',
            '1-2Y', 'O',
            '>2Y', 'P',
            'Others', 'Q',
        ${report3GroupBy})
    </select>

    <sql id="queryReport3DetailsSQL">
        SELECT *
          FROM ${SCPA.OPEN_PMS_STRUCTURE_V} T
        <where>
            <include refid="openPmsFilter"/>
            <if test="report3DetailsType != null and report3DetailsType != ''">
                AND ${report3GroupBy} IN (
                <foreach collection="report3DetailsType" separator="," item="item">
                    '${item}'
                </foreach>
                )
            </if>
        </where>
    </sql>

    <select id="queryReport3DetailsCount" resultType="java.lang.Integer">
        <include refid="global.count_header"/>
        <include refid="queryReport3DetailsSQL"/>
        <include refid="global.count_footer"/>
    </select>

    <select id="queryReport3Details" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="queryReport3DetailsSQL"/>
        <include refid="global.select_footer"/>
    </select>

    <resultMap id="report4ResultMap" type="com.scp.demand.bean.OpenPmsReport4Bean">
        <result property="CALENDAR_DATE" column="CALENDAR_DATE"/>
        <result property="NAME" column="NAME"/>
        <result property="VALUE" column="VALUE"/>
    </resultMap>

    <select id="queryReport4Legend" resultType="java.lang.String">
        <choose>
            <when test="report4ViewType =='DELIVERY_RANGE'.toString()">
                SELECT NAME FROM OPEN_PMS_STRUCTURE_FILTER_V T
                WHERE T.CATEGORY = #{report4ViewType, jdbcType=VARCHAR}
                ORDER BY
                DECODE(
                T.NAME,
                '0D', 'A',
                '0-1D', 'B',
                '1-2D', 'C',
                '2-3D', 'D',
                '3-7D', 'E',
                '5-7D', 'F',
                '3-4D', 'G',
                '1-2W', 'H',
                '2-4W', 'I',
                '1-2M', 'J',
                '2-3M', 'K',
                '3-6M', 'L',
                '6-12M', 'M',
                '6-1Y', 'N',
                '1-2Y', 'O',
                '>2Y', 'P',
                'Others', 'Q',
                T.NAME)
            </when>
            <otherwise>
                WITH BASE AS (
                    SELECT DISTINCT NVL(T.${report4ViewType}, 'Others') AS NAME FROM SCPA.OPEN_PMS_STRUCTURE_HIST T
                    <where>
                        <include refid="openPmsHistFilter"/>
                        <choose>
                            <when test='report4SelectedType == "VIEW_BY_DAY".toString()'/>
                            <when test='report4SelectedType == "VIEW_BY_WEEK".toString()'>
                                AND TO_CHAR(T.DATE$, 'D') = 7
                            </when>
                            <when test='report4SelectedType == "VIEW_BY_MONTH".toString()'>
                                AND TO_CHAR(T.DATE$, 'DD') = 1
                            </when>
                            <when test='report4SelectedType == "VIEW_BY_QUARTER".toString()'>
                                AND T.DATE$ = TRUNC(T.DATE$, 'Q')
                            </when>
                            <when test='report4SelectedType == "VIEW_BY_YEAR".toString()'>
                                AND T.DATE$ = TRUNC(T.DATE$, 'YYYY')
                            </when>
                            <otherwise>
                                AND TO_CHAR(T.DATE$, 'DD') = 1
                            </otherwise>
                        </choose>
                        AND T.DATE$ BETWEEN TO_DATE(#{report4DateRange[0], jdbcType=VARCHAR}, 'yyyy/mm/dd') AND TO_DATE(#{report4DateRange[1], jdbcType=VARCHAR}, 'yyyy/mm/dd')
                    </where>
                )
                SELECT * FROM BASE ORDER BY
                    <![CDATA[ DECODE(NVL(NAME, 'Others'),
                    '0D', 'A',
                    '0-1D', 'B',
                    '1-2D', 'C',
                    '2-3D', 'D',
                    '3-7D', 'E',
                    '5-7D', 'F',
                    '3-4D', 'G',
                    '1-2W', 'H',
                    '2-4W', 'I',
                    '1-2M', 'J',
                    '2-3M', 'K',
                    '3-6M', 'L',
                    '6-12M', 'M',
                    '6-1Y', 'N',
                    '1-2Y', 'O',
                    '>2Y', 'P',
                    'Others', 'Q',
                     NVL(NAME, 'Others'))]]>
            </otherwise>
        </choose>
    </select>

    <select id="queryReport4" resultMap="report4ResultMap">
        WITH BASE AS (
            SELECT /*+ parallel(t 6) */ * FROM ${SCPA.OPEN_PMS_STRUCTURE_HIST} T
            <where>
                <include refid="openPmsHistFilter"/>
                <choose>
                    <when test='report4SelectedType == "VIEW_BY_DAY".toString()'/>
                    <when test='report4SelectedType == "VIEW_BY_WEEK".toString()'>
                        AND TO_CHAR(T.DATE$, 'D') = 7
                    </when>
                    <when test='report4SelectedType == "VIEW_BY_MONTH".toString()'>
                        AND TO_CHAR(T.DATE$, 'DD') = 1
                    </when>
                    <when test='report4SelectedType == "VIEW_BY_QUARTER".toString()'>
                        AND T.DATE$ = TRUNC(T.DATE$, 'Q')
                    </when>
                    <when test='report4SelectedType == "VIEW_BY_YEAR".toString()'>
                        AND T.DATE$ = TRUNC(T.DATE$, 'YYYY')
                    </when>
                    <otherwise>
                        AND TO_CHAR(T.DATE$, 'DD') = 1
                    </otherwise>
                </choose>
                AND T.DATE$ IS NOT NULL
                AND T.DATE$ BETWEEN TO_DATE(#{report4DateRange[0], jdbcType=VARCHAR}, 'yyyy/mm/dd') AND TO_DATE(#{report4DateRange[1], jdbcType=VARCHAR}, 'yyyy/mm/dd')
            </where>
        )
        SELECT TO_CHAR(T.DATE$, 'yyyy/mm/dd') AS "CALENDAR_DATE",
               NVL(T.${report4ViewType}, 'Others') AS "NAME",
               ${valueColumn} AS "VALUE"
        FROM BASE T
        GROUP BY T.DATE$, NVL(T.${report4ViewType}, 'Others')
        ORDER BY
        DECODE(
        NVL(T.${report4ViewType}, 'Others'),
            '0D', 'A',
            '0-1D', 'B',
            '1-2D', 'C',
            '2-3D', 'D',
            '3-7D', 'E',
            '5-7D', 'F',
            '3-4D', 'G',
            '1-2W', 'H',
            '2-4W', 'I',
            '1-2M', 'J',
            '2-3M', 'K',
            '3-6M', 'L',
            '6-12M', 'M',
            '6-1Y', 'N',
            '1-2Y', 'O',
            '>2Y', 'P',
            'Others', 'Q',
        NVL(T.${report4ViewType}, 'Others'))
    </select>

    <sql id="queryReport4DetailsSQL">
        WITH <include refid="mv.so_backlog_hist_v"/>
        SELECT *
          FROM SO_BACKLOG_HIST_V T LEFT JOIN SY_CALENDAR CD ON T.CONFIRM_DATE = CD.DATE$ AND CD.NAME = 'National Holidays'
        <where>
            <include refid="openPmsHistFilter"/>
            AND T.DATE$ = TO_DATE(#{report4DetailsType, jdbcType=VARCHAR}, 'yyyy/mm/dd')
        </where>
    </sql>

    <select id="queryReport4DetailsCount" resultType="java.lang.Integer">
        <include refid="global.count_header"/>
        <include refid="queryReport4DetailsSQL"/>
        <include refid="global.count_footer"/>
    </select>

    <select id="queryReport4Details" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="queryReport4DetailsSQL"/>
        <include refid="global.select_footer"/>
    </select>

    <select id="queryReport5Columns" resultType="java.lang.String">
        SELECT distinct TO_CHAR(DATE$, 'YYYY/MM/DD') AS label
        FROM ${SCPA.OPEN_PMS_STRUCTURE_HIST} t
        WHERE T.DATE$ BETWEEN least(TO_DATE(#{report4DateRange[0], jdbcType=VARCHAR}, 'yyyy/mm/dd'),TRUNC(SYSDATE, 'dd'))
        AND least(TO_DATE(#{report4DateRange[1], jdbcType=VARCHAR}, 'yyyy/mm/dd'),TRUNC(SYSDATE, 'dd'))
        <choose>
            <when test='report4SelectedType == "VIEW_BY_DAY".toString()'/>
            <when test='report4SelectedType == "VIEW_BY_WEEK".toString()'>
                AND TO_CHAR(T.DATE$, 'D') = 7
            </when>
            <when test='report4SelectedType == "VIEW_BY_MONTH".toString()'>
                AND TO_CHAR(T.DATE$, 'DD') = 1
            </when>
            <when test='report4SelectedType == "VIEW_BY_QUARTER".toString()'>
                AND T.DATE$ = TRUNC(T.DATE$, 'Q')
            </when>
            <when test='report4SelectedType == "VIEW_BY_YEAR".toString()'>
                AND T.DATE$ = TRUNC(T.DATE$, 'yyyy"/01/01"')
            </when>
            <otherwise>
                AND TO_CHAR(T.DATE$, 'DD') = 1
            </otherwise>
        </choose>
        ORDER BY label DESC
        FETCH NEXT 63 ROWS ONLY
    </select>

    <select id="queryReport5" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        WITH BASE AS (
            SELECT /*+ parallel(t 6) */
            TO_CHAR(DATE$, 'YYYY/MM/DD') PDATE,
            <foreach collection="report5Columns" separator="," item="item">
                NVL(${item}, 'Others') AS "${item}"
            </foreach>,
            OPEN_PMS_QTY,
            AVG_SELLING_PRICE_RMB
            FROM ${SCPA.OPEN_PMS_STRUCTURE_HIST} T
            <where>
                <include refid="openPmsHistFilter"/>
                <choose>
                    <when test='report4SelectedType == "VIEW_BY_DAY".toString()'/>
                    <when test='report4SelectedType == "VIEW_BY_WEEK".toString()'>
                        AND TO_CHAR(T.DATE$, 'D') = 7
                    </when>
                    <when test='report4SelectedType == "VIEW_BY_MONTH".toString()'>
                        AND TO_CHAR(T.DATE$, 'DD') = 1
                    </when>
                    <when test='report4SelectedType == "VIEW_BY_QUARTER".toString()'>
                        AND T.DATE$ = TRUNC(T.DATE$, 'Q')
                    </when>
                    <when test='report4SelectedType == "VIEW_BY_YEAR".toString()'>
                        AND T.DATE$ = TRUNC(T.DATE$, 'YYYY')
                    </when>
                    <otherwise>
                        AND TO_CHAR(T.DATE$, 'DD') = 1
                    </otherwise>
                </choose>
                AND T.DATE$ BETWEEN TO_DATE(#{report4DateRange[0], jdbcType=VARCHAR}, 'yyyy/mm/dd') AND TO_DATE(#{report4DateRange[1], jdbcType=VARCHAR}, 'yyyy/mm/dd')
            </where>
        )
        SELECT *
          FROM (
              SELECT PDATE,
                     <foreach collection="report5Columns" separator="," item="item">
                        ${item}
                     </foreach>,
                     ${valueColumn} AS TOTAL
              FROM BASE T0
              GROUP BY PDATE,
              <foreach collection="report5Columns" separator="," item="item">
                 ${item}
              </foreach>
        ) T PIVOT (
            SUM(TOTAL) AS TOTAL
            FOR PDATE IN (
            <foreach collection="report5DateColumns" separator="," item="item">
                '${item}'
            </foreach>)
        )
        ORDER BY
        <foreach collection="report5Columns" separator="," item="item">
            DECODE(
            ${item},
            '0D', 'A',
            '0-1D', 'B',
            '1-2D', 'C',
            '2-3D', 'D',
            '3-7D', 'E',
            '5-7D', 'F',
            '3-4D', 'G',
            '1-2W', 'H',
            '2-4W', 'I',
            '1-2M', 'J',
            '2-3M', 'K',
            '3-6M', 'L',
            '6-12M', 'M',
            '6-1Y', 'N',
            '1-2Y', 'O',
            '>2Y', 'P',
            'Others', 'Q',
            ${item})
        </foreach>
        FETCH FIRST 5000 ROWS ONLY
        <include refid="global.select_footer"/>
    </select>

    <sql id="queryReport5DetailsSQL">
        SELECT *
          FROM ${SCPA.OPEN_PMS_STRUCTURE_HIST} T
            <where>
                <include refid="openPmsHistFilter"/>
                <if test="report5DetailsType[0] != ''.toString()">
                    AND T.DATE$ = TO_DATE(#{report5DetailsType[0], jdbcType=VARCHAR}, 'yyyy/mm/dd')
                </if>
            </where>

            <foreach collection="report5DetailsType" index="index" item="item">
                <if test="index > 0">
                    <if test="report5DetailsType[index] != 'Total'.toString()">
                        AND T.${report5Columns[index - 1]} = #{item, jdbcType=VARCHAR}
                    </if>
                </if>
            </foreach>

    </sql>

    <select id="queryReport5DetailsCount" resultType="java.lang.Integer">
        <include refid="global.count_header"/>
        <include refid="queryReport5DetailsSQL"/>
        <include refid="global.count_footer"/>
    </select>

    <select id="queryReport5Details" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="queryReport5DetailsSQL"/>
        <include refid="global.select_footer"/>
    </select>
</mapper>
