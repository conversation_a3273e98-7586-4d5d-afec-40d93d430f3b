package com.agt.agent.dao;

import com.starter.context.bean.Response;
import org.apache.ibatis.annotations.Mapper;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Mapper
public interface IMossDao {

    List<LinkedHashMap<String, Object>> querySampleData(String sql);

    int queryDataBySqlCount(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryDataBySql(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryChartBySql(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryTreeMapBySql(Map<String, Object> parameterMap);

    void saveGoodEvaluateResult(Map<String, Object> parameterMap);

    void saveEvaluateResult(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryTableTips(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryChatLogs(Map<String, Object> parameterMap);

    Map<String, Object> queryChatById(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryAllChatLogs(Map<String, Object> parameterMap);

    List<String> querySuggestQuestions(Map<String, Object> parameterMap);

    List<Map<String, String>> queryAllAgents(Map<String, Object> parameterMap);

    Map<String, String> queryAgentById(Map<String, Object> parameterMap);

    void addToArchive(Map<String, Object> parameterMap);

    List<Map<String, String>> queryMyArchive(Map<String, Object> parameterMap);

    void deleteMyArchive(Map<String, Object> parameterMap);
}
