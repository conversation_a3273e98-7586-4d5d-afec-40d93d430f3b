package com.starter.context.configuration.database;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;

/**
 * 数据源切换AOP切面
 * 自动处理@TargetDataSource注解，实现动态数据源切换
 */
@Aspect
@Component
@Order(1) // 确保在事务切面之前执行
public class DataSourceAspect {
    
    private static final Logger logger = LoggerFactory.getLogger(DataSourceAspect.class);

    /**
     * 拦截所有带有@TargetDataSource注解的方法
     */
    @Around("@annotation(com.starter.context.configuration.database.TargetDataSource)")
    public Object around(ProceedingJoinPoint point) throws Throwable {
        MethodSignature signature = (MethodSignature) point.getSignature();
        Method method = signature.getMethod();
        
        // 获取@TargetDataSource注解
        TargetDataSource targetDataSource = method.getAnnotation(TargetDataSource.class);
        if (targetDataSource == null) {
            // 如果方法上没有注解，检查类上是否有注解
            targetDataSource = method.getDeclaringClass().getAnnotation(TargetDataSource.class);
        }
        
        if (targetDataSource != null) {
            DatabaseType targetType = targetDataSource.value();
            DatabaseType originalType = DatabaseContextHolder.getDatabaseType();
            
            logger.debug("Switching database from {} to {} for method: {}", 
                        originalType, targetType, method.getName());
            
            // 切换数据源
            DatabaseContextHolder.setDatabaseType(targetType);
            
            try {
                // 执行目标方法
                Object result = point.proceed();
                logger.debug("Method {} executed successfully with database type: {}", 
                           method.getName(), targetType);
                return result;
            } catch (Exception e) {
                logger.error("Error executing method {} with database type {}: {}", 
                           method.getName(), targetType, e.getMessage());
                throw e;
            } finally {
                // 恢复原始数据源
                if (originalType != null) {
                    DatabaseContextHolder.setDatabaseType(originalType);
                } else {
                    DatabaseContextHolder.clearDatabaseType();
                }
                logger.debug("Database type restored to: {}", DatabaseContextHolder.getDatabaseType());
            }
        } else {
            // 没有注解，直接执行
            return point.proceed();
        }
    }
}
